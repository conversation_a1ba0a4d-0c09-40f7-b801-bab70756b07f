<?php


use App\Http\Controllers\HomeController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\FixtureController;
use App\Http\Controllers\PlayerController;
use App\Http\Controllers\MerchandiseController;
use App\Http\Controllers\MediaGalleryController;
use App\Http\Controllers\CartController;
use App\Http\Controllers\TicketController;
use App\Http\Controllers\Admin\DashboardController;
use Illuminate\Support\Facades\Route;

// Public Routes
Route::get('/', [HomeController::class, 'index'])->name('home');

// News Routes
Route::get('/news', [NewsController::class, 'index'])->name('news.index');
Route::get('/news/{news:slug}', [NewsController::class, 'show'])->name('news.show');

// Fixtures Routes
Route::get('/fixtures', [FixtureController::class, 'index'])->name('fixtures.index');

// Squad Routes
Route::get('/squad', [PlayerController::class, 'index'])->name('squad.index');
Route::get('/players/{player}', [PlayerController::class, 'show'])->name('players.show');

// Club Pages
Route::get('/club', [App\Http\Controllers\ClubController::class, 'index'])->name('club.index');
Route::get('/club/history', [App\Http\Controllers\ClubController::class, 'history'])->name('club.history');

// Tickets Routes
Route::get('/tickets', [TicketController::class, 'index'])->name('tickets.index');
Route::get('/tickets/{fixture}', [TicketController::class, 'show'])->name('tickets.show');
Route::post('/tickets/{fixture}/book', [TicketController::class, 'book'])->name('tickets.book');

// Store Routes
Route::get('/store', [MerchandiseController::class, 'index'])->name('store.index');
Route::get('/store/{merchandise}', [MerchandiseController::class, 'show'])->name('store.show');

// Media Gallery
Route::get('/media', [MediaGalleryController::class, 'index'])->name('media.index');

// Contact Routes
Route::get('/contact', [App\Http\Controllers\ContactController::class, 'index'])->name('contact.index');
Route::post('/contact', [App\Http\Controllers\ContactController::class, 'store'])->name('contact.store');

// Cart Routes (removed authentication requirement)
Route::get('/cart', [CartController::class, 'index'])->name('cart.index');
Route::post('/cart/add/{merchandise}', [CartController::class, 'add'])->name('cart.add');
Route::patch('/cart/{merchandise}', [CartController::class, 'update'])->name('cart.update');
Route::delete('/cart/{merchandise}', [CartController::class, 'remove'])->name('cart.remove');
Route::post('/cart/checkout', [CartController::class, 'checkout'])->name('cart.checkout');

// Voting Routes
Route::get('/api/motm-candidates', [App\Http\Controllers\VotingController::class, 'getCandidates'])->name('api.motm.candidates');
Route::post('/api/motm-vote', [App\Http\Controllers\VotingController::class, 'vote'])->name('api.motm.vote');
Route::get('/api/motm-has-voted', [App\Http\Controllers\VotingController::class, 'hasVoted'])->name('api.motm.has-voted');

// Dashboard Route (redirects based on user role)
Route::middleware(['auth', 'verified'])->get('/dashboard', function () {
    if (auth()->user()->canManageContent()) {
        return redirect()->route('admin.dashboard');
    }
    // For regular users, redirect to home for now
    return redirect()->route('home');
})->name('dashboard');

// Profile Routes (redirect to admin panel for authenticated users)
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/profile', function () {
        if (auth()->user()->canManageContent()) {
            return redirect()->route('admin.profile.edit');
        }
        return redirect()->route('home');
    })->name('profile.edit');
});

// Admin Routes (for admin and editor roles)
Route::middleware(['auth', 'verified', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

    // News Management
    Route::resource('news', App\Http\Controllers\Admin\NewsController::class);
    Route::resource('news-categories', App\Http\Controllers\Admin\NewsCategoryController::class);
    Route::post('news-categories/{newsCategory}/toggle', [App\Http\Controllers\Admin\NewsCategoryController::class, 'toggle'])->name('admin.news-categories.toggle');

    // Player Management
    Route::resource('players', App\Http\Controllers\Admin\PlayerController::class);

    // Fixture Management
    Route::resource('fixtures', App\Http\Controllers\Admin\FixtureController::class);

    // Merchandise Management
    Route::resource('merchandise', App\Http\Controllers\Admin\MerchandiseController::class);

    // Media Gallery Management
    Route::resource('media', App\Http\Controllers\Admin\MediaGalleryController::class);

    // Sponsor Management
    Route::resource('sponsors', App\Http\Controllers\Admin\SponsorController::class);

    // Settings Management
    Route::get('/settings', [App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('settings.index');
    Route::put('/settings', [App\Http\Controllers\Admin\SettingsController::class, 'update'])->name('settings.update');

    // Page Content Management
    Route::get('page-content/{pageKey}', [App\Http\Controllers\Admin\SettingsController::class, 'getPageContent'])->name('admin.page-content.get');
    Route::post('page-content', [App\Http\Controllers\Admin\SettingsController::class, 'storePageContent'])->name('admin.page-content.store');

    // Timeline Events Management
    Route::get('timeline-events', [App\Http\Controllers\Admin\SettingsController::class, 'getTimelineEvents'])->name('admin.timeline-events.get');
    Route::post('timeline-events', [App\Http\Controllers\Admin\SettingsController::class, 'storeTimelineEvent'])->name('admin.timeline-events.store');
    Route::delete('timeline-events', [App\Http\Controllers\Admin\SettingsController::class, 'deleteTimelineEvent'])->name('admin.timeline-events.delete');

    // Profile Management
    Route::get('/profile', [App\Http\Controllers\Admin\ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [App\Http\Controllers\Admin\ProfileController::class, 'update'])->name('profile.update');
    Route::put('/profile/password', [App\Http\Controllers\Admin\ProfileController::class, 'updatePassword'])->name('profile.password');
    Route::delete('/profile', [App\Http\Controllers\Admin\ProfileController::class, 'destroy'])->name('profile.destroy');

    // Man of the Match Management
    Route::get('/motm', [App\Http\Controllers\Admin\MotmController::class, 'index'])->name('motm.index');
    Route::get('/motm/create', [App\Http\Controllers\Admin\MotmController::class, 'create'])->name('motm.create');
    Route::post('/motm', [App\Http\Controllers\Admin\MotmController::class, 'store'])->name('motm.store');
    Route::delete('/motm/{motmCandidate}', [App\Http\Controllers\Admin\MotmController::class, 'destroy'])->name('motm.destroy');
    Route::patch('/motm/{motmCandidate}/toggle', [App\Http\Controllers\Admin\MotmController::class, 'toggleActive'])->name('motm.toggle');

    // Contact Messages Management
    Route::get('/contact-messages', [App\Http\Controllers\Admin\ContactMessageController::class, 'index'])->name('contact-messages.index');
    Route::get('/contact-messages/{contactMessage}', [App\Http\Controllers\Admin\ContactMessageController::class, 'show'])->name('contact-messages.show');
    Route::delete('/contact-messages/{contactMessage}', [App\Http\Controllers\Admin\ContactMessageController::class, 'destroy'])->name('contact-messages.destroy');
    Route::patch('/contact-messages/{contactMessage}/read', [App\Http\Controllers\Admin\ContactMessageController::class, 'markAsRead'])->name('contact-messages.read');
    Route::patch('/contact-messages/{contactMessage}/unread', [App\Http\Controllers\Admin\ContactMessageController::class, 'markAsUnread'])->name('contact-messages.unread');
});

// Include authentication routes
require __DIR__.'/auth.php';
