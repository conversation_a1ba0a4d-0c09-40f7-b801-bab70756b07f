<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use App\Models\Setting;
use App\Services\MetricsService;

class SettingsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Share settings with all views
        View::composer('*', function ($view) {
            $settings = Setting::getMultiple([
                'site_name',
                'site_description',
                'contact_email',
                'contact_phone',
                'facebook_url',
                'twitter_url',
                'instagram_url',
                'youtube_url',
                'founded_year',
                'home_stadium',
                'stadium_capacity',
                'club_colors',
                'meta_keywords',
                'contact_address',
                'contact_map_iframe',
                'hero_title',
                'hero_subtitle',
                'about_title',
                'about_description',
                'mission_statement',
                'vision_statement',
                'values_statement'
            ]);

            // Merge with auto-derived metrics
            $settingsWithMetrics = MetricsService::mergeWithSettings($settings);

            $view->with('siteSettings', $settingsWithMetrics);
        });
    }
}
