<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class TimelineEvent extends Model
{
    use HasFactory;

    protected $fillable = [
        'year',
        'title',
        'description',
        'achievement',
        'color',
        'sort_order',
        'is_active'
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
        ];
    }

    /**
     * Scope for active events
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered events
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('year')->orderBy('sort_order');
    }
}
