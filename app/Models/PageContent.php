<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PageContent extends Model
{
    use HasFactory;

    protected $fillable = [
        'page_key',
        'title',
        'content',
        'image',
        'meta_data',
        'is_active'
    ];

    protected function casts(): array
    {
        return [
            'meta_data' => 'array',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Scope for active pages
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get page content by key
     */
    public static function getByKey(string $key)
    {
        return static::where('page_key', $key)->active()->first();
    }
}
