<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MotmVote extends Model
{
    protected $fillable = [
        'motm_candidate_id',
        'voter_ip',
        'voter_cookie'
    ];

    public function candidate(): BelongsTo
    {
        return $this->belongsTo(MotmCandidate::class, 'motm_candidate_id');
    }

    public static function hasVoted(string $voterCookie): bool
    {
        return static::where('voter_cookie', $voterCookie)->exists();
    }
}
