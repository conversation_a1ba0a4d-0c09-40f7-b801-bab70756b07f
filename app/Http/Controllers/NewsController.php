<?php

namespace App\Http\Controllers;

use App\Models\News;
use App\Models\NewsCategory;
use Illuminate\Http\Request;

class NewsController extends Controller
{
    public function index(Request $request)
    {
        $query = News::published()->with('author');

        // Filter by category if provided
        if ($request->has('category') && $request->category) {
            $query->where('category_id', $request->category);
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('body', 'like', '%' . $request->search . '%');
            });
        }

        $news = $query->latest('published_at')->paginate(12);

        // Get categories for filter from admin-created categories
        $categories = NewsCategory::active()->orderBy('name')->get();

        return view('news.index', compact('news', 'categories'));
    }

    public function show(News $news, Request $request)
    {
        // Only show published articles
        if ($news->status !== 'published') {
            abort(404);
        }

        // Track view with cookie (permanent until news is deleted)
        $cookieName = 'news_viewed_' . $news->id;

        if (!$request->hasCookie($cookieName)) {
            // Increment view count
            $news->increment('views');

            // Set permanent cookie for this news article
            cookie()->queue($cookieName, '1', 525600); // 1 year in minutes
        }

        // Load the author relationship
        $news->load('author');

        // Get related articles (same category, excluding current)
        $relatedNews = News::published()
            ->where('category_id', $news->category_id)
            ->where('id', '!=', $news->id)
            ->latest('published_at')
            ->take(3)
            ->get();

        return view('news.show', compact('news', 'relatedNews'));
    }
}
