<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Player;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class PlayerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $players = Player::orderBy('shirt_number')
            ->paginate(20);

        return view('admin.players.index', compact('players'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.players.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'position' => 'required|in:GK,DF,MF,FW',
            'shirt_number' => 'required|integer|min:1|max:99|unique:players,shirt_number',
            'nationality' => 'required|string|max:255',
            'age' => 'nullable|integer|min:16|max:45',
            'bio' => 'nullable|string',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'goals' => 'nullable|integer|min:0',
            'assists' => 'nullable|integer|min:0',
            'appearances' => 'nullable|integer|min:0',
            'yellow_cards' => 'nullable|integer|min:0',
            'red_cards' => 'nullable|integer|min:0',
            'social_media.instagram' => 'nullable|url',
            'social_media.twitter' => 'nullable|url',
            'social_media.facebook' => 'nullable|url',
            'previous_clubs.*.name' => 'nullable|string|max:255',
        ]);

        $data = $request->all();

        // Handle photo upload
        if ($request->hasFile('photo')) {
            $data['photo'] = $request->file('photo')->store('players', 'public');
        }

        // Prepare stats array
        $data['stats'] = [
            'goals' => $request->goals ?? 0,
            'assists' => $request->assists ?? 0,
            'appearances' => $request->appearances ?? 0,
            'yellow_cards' => $request->yellow_cards ?? 0,
            'red_cards' => $request->red_cards ?? 0,
        ];

        // Prepare social media array (filter out empty values)
        $socialMedia = array_filter($request->social_media ?? []);
        $data['social_media'] = !empty($socialMedia) ? $socialMedia : null;

        // Prepare previous clubs array (filter out empty names)
        $previousClubs = array_filter($request->previous_clubs ?? [], function($club) {
            return !empty($club['name']);
        });
        $data['previous_clubs'] = !empty($previousClubs) ? array_values($previousClubs) : null;

        // Remove individual stat fields from data
        unset($data['goals'], $data['assists'], $data['appearances'], $data['yellow_cards'], $data['red_cards']);

        Player::create($data);

        return redirect()->route('admin.players.index')
            ->with('success', 'Player created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Player $player)
    {
        return view('admin.players.show', compact('player'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Player $player)
    {
        return view('admin.players.edit', compact('player'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Player $player)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'position' => 'required|in:GK,DF,MF,FW',
            'shirt_number' => 'required|integer|min:1|max:99|unique:players,shirt_number,' . $player->id,
            'nationality' => 'required|string|max:255',
            'age' => 'nullable|integer|min:16|max:45',
            'bio' => 'nullable|string',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'goals' => 'nullable|integer|min:0',
            'assists' => 'nullable|integer|min:0',
            'appearances' => 'nullable|integer|min:0',
            'yellow_cards' => 'nullable|integer|min:0',
            'red_cards' => 'nullable|integer|min:0',
            'social_media.instagram' => 'nullable|url',
            'social_media.twitter' => 'nullable|url',
            'social_media.facebook' => 'nullable|url',
            'previous_clubs.*.name' => 'nullable|string|max:255',
        ]);

        $data = $request->all();

        // Handle photo upload
        if ($request->hasFile('photo')) {
            // Delete old photo if exists
            if ($player->photo) {
                Storage::disk('public')->delete($player->photo);
            }
            $data['photo'] = $request->file('photo')->store('players', 'public');
        }

        // Prepare stats array
        $data['stats'] = [
            'goals' => $request->goals ?? 0,
            'assists' => $request->assists ?? 0,
            'appearances' => $request->appearances ?? 0,
            'yellow_cards' => $request->yellow_cards ?? 0,
            'red_cards' => $request->red_cards ?? 0,
        ];

        // Prepare social media array (filter out empty values)
        $socialMedia = array_filter($request->social_media ?? []);
        $data['social_media'] = !empty($socialMedia) ? $socialMedia : null;

        // Prepare previous clubs array (filter out empty names)
        $previousClubs = array_filter($request->previous_clubs ?? [], function($club) {
            return !empty($club['name']);
        });
        $data['previous_clubs'] = !empty($previousClubs) ? array_values($previousClubs) : null;

        // Remove individual stat fields from data
        unset($data['goals'], $data['assists'], $data['appearances'], $data['yellow_cards'], $data['red_cards']);

        $player->update($data);

        return redirect()->route('admin.players.index')
            ->with('success', 'Player updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Player $player)
    {
        // Delete photo if exists
        if ($player->photo) {
            Storage::disk('public')->delete($player->photo);
        }

        $player->delete();

        return redirect()->route('admin.players.index')
            ->with('success', 'Player deleted successfully.');
    }
}
