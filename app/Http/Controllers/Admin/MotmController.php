<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\MotmCandidate;
use App\Models\Player;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class MotmController extends Controller
{
    public function index()
    {
        $candidates = MotmCandidate::with(['player', 'votes'])
            ->orderBy('is_active', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('admin.motm.index', compact('candidates'));
    }

    public function create()
    {
        $players = Player::orderBy('name')->get();
        return view('admin.motm.create', compact('players'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'player_ids' => 'required|array|min:1|max:3',
            'player_ids.*' => 'exists:players,id',
            'match_description' => 'required|string|max:255',
            'match_date' => 'required|date',
            'expiration_hours' => 'nullable|integer|min:1',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Deactivate all current candidates
        MotmCandidate::where('is_active', true)->update(['is_active' => false]);

        // Calculate expiration time
        $expiresAt = null;
        if ($request->expiration_hours) {
            $expiresAt = now()->addHours((int)$request->expiration_hours);
        }

        // Create new candidates
        foreach ($request->player_ids as $playerId) {
            MotmCandidate::create([
                'player_id' => $playerId,
                'match_description' => $request->match_description,
                'match_date' => $request->match_date,
                'is_active' => true,
                'expires_at' => $expiresAt,
            ]);
        }

        return redirect()->route('admin.motm.index')
            ->with('success', 'Man of the Match candidates updated successfully!');
    }

    public function destroy(MotmCandidate $motmCandidate)
    {
        $motmCandidate->delete();

        return redirect()->route('admin.motm.index')
            ->with('success', 'Candidate deleted successfully!');
    }

    public function toggleActive(MotmCandidate $motmCandidate)
    {
        $motmCandidate->update(['is_active' => !$motmCandidate->is_active]);

        return redirect()->route('admin.motm.index')
            ->with('success', 'Candidate status updated successfully!');
    }
}
