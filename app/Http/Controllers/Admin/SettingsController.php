<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use App\Models\PageContent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class SettingsController extends Controller
{
    /**
     * Display the settings page
     */
    public function index()
    {
        $settings = Setting::getMultiple([
            'site_name',
            'site_description',
            'contact_email',
            'contact_phone',
            'contact_address',
            'contact_map_iframe',
            'facebook_url',
            'twitter_url',
            'instagram_url',
            'youtube_url',
            'founded_year',
            'home_stadium',
            'stadium_capacity',
            'club_colors',
            'meta_keywords',
            'hero_title',
            'hero_subtitle',
            'about_title',
            'about_description',
            'mission_statement',
            'vision_statement',
            'values_statement',
            'about_overview_title',
            'about_overview_content',
            'about_overview_image',
            'total_trophies',
            'registered_fans',
            'stadium_name',
            'stadium_location',
            'stadium_description',
            'stadium_standard',
            'stadium_image',
            'total_matches_played',
            'total_victories',
            'total_goals_scored',
            'total_clean_sheets'
        ]);

        $aboutPage = PageContent::getByKey('about');
        $historyPage = PageContent::getByKey('history');

        return view('admin.settings.index', compact('settings', 'aboutPage', 'historyPage'));
    }

    /**
     * Update settings
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'site_name' => 'required|string|max:255',
            'site_description' => 'required|string|max:500',
            'contact_email' => 'required|email|max:255',
            'contact_phone' => 'required|string|max:20',
            'contact_address' => 'required|string|max:255',
            'contact_map_iframe' => 'nullable|string',
            'facebook_url' => 'nullable|url|max:255',
            'twitter_url' => 'nullable|url|max:255',
            'instagram_url' => 'nullable|url|max:255',
            'youtube_url' => 'nullable|url|max:255',
            'founded_year' => 'required|integer|min:1900|max:' . date('Y'),
            'home_stadium' => 'required|string|max:255',
            'stadium_capacity' => 'required|integer|min:1',
            'club_colors' => 'required|string|max:255',
            'meta_keywords' => 'nullable|string|max:500',
            'hero_title' => 'nullable|string|max:255',
            'hero_subtitle' => 'nullable|string|max:500',
            'about_title' => 'nullable|string|max:255',
            'about_description' => 'nullable|string',
            'mission_statement' => 'nullable|string',
            'vision_statement' => 'nullable|string',
            'values_statement' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Get current settings for file handling
        $currentSettings = Setting::pluck('value', 'key')->toArray();

        // Handle file uploads
        if ($request->hasFile('about_overview_image')) {
            if ($currentSettings['about_overview_image'] ?? null) {
                Storage::disk('public')->delete($currentSettings['about_overview_image']);
            }
            $aboutOverviewImage = $request->file('about_overview_image')->store('about', 'public');
            Setting::set('about_overview_image', $aboutOverviewImage);
        }

        if ($request->hasFile('stadium_image')) {
            if ($currentSettings['stadium_image'] ?? null) {
                Storage::disk('public')->delete($currentSettings['stadium_image']);
            }
            $stadiumImage = $request->file('stadium_image')->store('stadium', 'public');
            Setting::set('stadium_image', $stadiumImage);
        }

        // Update all settings
        foreach ($request->only([
            'site_name', 'site_description', 'contact_email', 'contact_phone', 'contact_address',
            'contact_map_iframe', 'facebook_url', 'twitter_url', 'instagram_url', 'youtube_url',
            'founded_year', 'home_stadium', 'stadium_capacity', 'club_colors', 'meta_keywords',
            'hero_title', 'hero_subtitle', 'about_title', 'about_description',
            'mission_statement', 'vision_statement', 'values_statement',
            'about_overview_title', 'about_overview_content', 'total_trophies', 'registered_fans',
            'stadium_name', 'stadium_location', 'stadium_description', 'stadium_standard',
            'total_matches_played', 'total_victories', 'total_goals_scored', 'total_clean_sheets'
        ]) as $key => $value) {
            Setting::set($key, $value);
        }

        return redirect()->route('admin.settings.index')
            ->with('success', 'Settings updated successfully!');
    }

    /**
     * Get page content by key
     */
    public function getPageContent($pageKey)
    {
        $page = PageContent::getByKey($pageKey);

        return response()->json([
            'success' => true,
            'page' => $page
        ]);
    }

    /**
     * Store or update page content
     */
    public function storePageContent(Request $request)
    {
        $request->validate([
            'page_key' => 'required|in:about,history',
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->only(['page_key', 'title', 'content']);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            $existingPage = PageContent::where('page_key', $request->page_key)->first();
            if ($existingPage && $existingPage->image) {
                Storage::disk('public')->delete($existingPage->image);
            }

            $data['image'] = $request->file('image')->store('pages', 'public');
        }

        PageContent::updateOrCreate(
            ['page_key' => $request->page_key],
            $data
        );

        return response()->json([
            'success' => true,
            'message' => 'Page content saved successfully!'
        ]);
    }
}
