<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use App\Models\PageContent;
use App\Models\TimelineEvent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class SettingsController extends Controller
{
    /**
     * Display the settings page
     */
    public function index()
    {
        $settings = Setting::getMultiple([
            'site_name',
            'site_description',
            'contact_email',
            'contact_phone',
            'contact_address',
            'contact_map_iframe',
            'facebook_url',
            'twitter_url',
            'instagram_url',
            'youtube_url',
            'founded_year',
            'home_stadium',
            'stadium_capacity',
            'club_colors',
            'meta_keywords',
            'hero_title',
            'hero_subtitle',
            'about_description',
            'mission_statement',
            'vision_statement',
            'values_statement',
            'about_overview_content',
            'about_overview_title',
            'about_overview_image',
            'total_trophies',
            'registered_fans',
            'stadium_name',
            'stadium_location',
            'stadium_description',
            'stadium_standard',
            'stadium_image',
            'total_matches_played',
            'total_victories',
            'total_goals_scored',
            'total_clean_sheets',
            'total_fans',
            'total_wins',
            'win_rate',
            'season_progress',
            'total_events',
            'total_readers',
            'years_experience',
            'matches_per_season',
            'current_league',
            'league_titles',
            'cup_victories',
            'community_awards'
        ]);

        $aboutPage = PageContent::getByKey('about');
        $historyPage = PageContent::getByKey('history');
        $timelineEvents = TimelineEvent::orderBy('year')->orderBy('sort_order')->get();

        return view('admin.settings.index', compact('settings', 'aboutPage', 'historyPage', 'timelineEvents'));
    }

    /**
     * Update settings
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'site_name' => 'required|string|max:255',
            'site_description' => 'required|string|max:500',
            'contact_email' => 'required|email|max:255',
            'contact_phone' => 'required|string|max:20',
            'contact_address' => 'required|string|max:255',
            'contact_map_iframe' => 'nullable|string',
            'facebook_url' => 'nullable|url|max:255',
            'twitter_url' => 'nullable|url|max:255',
            'instagram_url' => 'nullable|url|max:255',
            'youtube_url' => 'nullable|url|max:255',
            'founded_year' => 'required|integer|min:1900|max:' . date('Y'),
            'home_stadium' => 'required|string|max:255',
            'stadium_capacity' => 'required|integer|min:1',
            'club_colors' => 'required|string|max:255',
            'meta_keywords' => 'nullable|string|max:500',
            'hero_title' => 'nullable|string|max:255',
            'hero_subtitle' => 'nullable|string|max:500',
            'about_description' => 'nullable|string',
            'mission_statement' => 'nullable|string',
            'vision_statement' => 'nullable|string',
            'values_statement' => 'nullable|string',
            // About page fields
            'about_overview_content' => 'nullable|string',
            'about_overview_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'about_overview_title' => 'nullable|string',
            // Stadium fields
            'stadium_name' => 'nullable|string|max:255',
            'stadium_location' => 'nullable|string|max:255',
            'stadium_description' => 'nullable|string',
            'stadium_standard' => 'nullable|string|max:255',
            'stadium_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            // Metrics fields
            'total_trophies' => 'nullable|integer|min:0',
            'registered_fans' => 'nullable|integer|min:0',
            'total_matches_played' => 'nullable|integer|min:0',
            'total_victories' => 'nullable|integer|min:0',
            'total_goals_scored' => 'nullable|integer|min:0',
            'total_clean_sheets' => 'nullable|integer|min:0',
            // Additional metrics
            'total_fans' => 'nullable|integer|min:0',
            'total_wins' => 'nullable|integer|min:0',
            'win_rate' => 'nullable|integer|min:0|max:100',
            'season_progress' => 'nullable|integer|min:0|max:100',
            'total_events' => 'nullable|integer|min:0',
            'total_readers' => 'nullable|integer|min:0',
            'years_experience' => 'nullable|integer|min:0',
            'matches_per_season' => 'nullable|integer|min:1',
            'current_league' => 'nullable|string|max:255',
            'league_titles' => 'nullable|integer|min:0',
            'cup_victories' => 'nullable|integer|min:0',
            'community_awards' => 'nullable|integer|min:0'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Get current settings for file handling
        $currentSettings = Setting::pluck('value', 'key')->toArray();

        // Handle file uploads
        if ($request->hasFile('about_overview_image')) {
            if ($currentSettings['about_overview_image'] ?? null) {
                Storage::disk('public')->delete($currentSettings['about_overview_image']);
            }
            $aboutOverviewImage = $request->file('about_overview_image')->store('about', 'public');
            Setting::set('about_overview_image', $aboutOverviewImage);
        }

        if ($request->hasFile('stadium_image')) {
            if ($currentSettings['stadium_image'] ?? null) {
                Storage::disk('public')->delete($currentSettings['stadium_image']);
            }
            $stadiumImage = $request->file('stadium_image')->store('stadium', 'public');
            Setting::set('stadium_image', $stadiumImage);
        }

        // Update all settings
        foreach ($request->only([
            'site_name', 'site_description', 'contact_email', 'contact_phone', 'contact_address',
            'contact_map_iframe', 'facebook_url', 'twitter_url', 'instagram_url', 'youtube_url',
            'founded_year', 'home_stadium', 'stadium_capacity', 'club_colors', 'meta_keywords',
            'hero_title', 'hero_subtitle', 'about_title', 'about_description',
            'mission_statement', 'vision_statement', 'values_statement', 'about_overview_title',
            'about_overview_content', 'total_trophies', 'registered_fans',
            'stadium_name', 'stadium_location', 'stadium_description', 'stadium_standard',
            'total_matches_played', 'total_victories', 'total_goals_scored', 'total_clean_sheets',
            'total_fans', 'total_wins', 'win_rate', 'season_progress', 'total_events', 'total_readers',
            'years_experience', 'matches_per_season', 'current_league', 'league_titles', 'cup_victories', 'community_awards'
        ]) as $key => $value) {
            Setting::set($key, $value);
        }

        return redirect()->route('admin.settings.index')
            ->with('success', 'Settings updated successfully!');
    }

    /**
     * Get page content by key
     */
    public function getPageContent($pageKey)
    {
        $page = PageContent::getByKey($pageKey);

        return response()->json([
            'success' => true,
            'page' => $page
        ]);
    }

    /**
     * Store or update page content
     */
    public function storePageContent(Request $request)
    {
        $request->validate([
            'page_key' => 'required|in:about,history',
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->only(['page_key', 'title', 'content']);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            $existingPage = PageContent::where('page_key', $request->page_key)->first();
            if ($existingPage && $existingPage->image) {
                Storage::disk('public')->delete($existingPage->image);
            }

            $data['image'] = $request->file('image')->store('pages', 'public');
        }

        // Ensure is_active is set to true
        $data['is_active'] = true;

        PageContent::updateOrCreate(
            ['page_key' => $request->page_key],
            $data
        );

        return response()->json([
            'success' => true,
            'message' => 'Page content saved successfully!'
        ]);
    }

    /**
     * Get all timeline events
     */
    public function getTimelineEvents()
    {
        $events = TimelineEvent::orderBy('year')->orderBy('sort_order')->get();

        return response()->json([
            'success' => true,
            'events' => $events
        ]);
    }

    /**
     * Store or update timeline event
     */
    public function storeTimelineEvent(Request $request)
    {
        $request->validate([
            'year' => 'required|integer|min:1900|max:' . date('Y'),
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'achievement' => 'nullable|string|max:255',
            'color' => 'required|string|max:7', // hex color
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean'
        ]);

        $data = $request->all();
        $data['is_active'] = $request->has('is_active');
        $data['sort_order'] = $data['sort_order'] ?? 0;

        if ($request->has('id') && $request->id) {
            $event = TimelineEvent::findOrFail($request->id);
            $event->update($data);
            $message = 'Timeline event updated successfully!';
        } else {
            TimelineEvent::create($data);
            $message = 'Timeline event created successfully!';
        }

        return response()->json([
            'success' => true,
            'message' => $message
        ]);
    }

    /**
     * Delete timeline event
     */
    public function deleteTimelineEvent(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:timeline_events,id'
        ]);

        TimelineEvent::findOrFail($request->id)->delete();

        return response()->json([
            'success' => true,
            'message' => 'Timeline event deleted successfully!'
        ]);
    }
}
