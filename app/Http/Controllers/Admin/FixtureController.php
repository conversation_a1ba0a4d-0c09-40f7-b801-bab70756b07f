<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Fixture;
use Illuminate\Http\Request;

class FixtureController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $fixtures = Fixture::orderBy('match_date', 'desc')
            ->paginate(15);

        return view('admin.fixtures.index', compact('fixtures'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.fixtures.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'opponent' => 'required|string|max:255',
            'competition' => 'required|string|max:255',
            'match_date' => [
                'required',
                'date',
                function ($attribute, $value, $fail) use ($request) {
                    if ($request->status === 'upcoming' && $value < now()->toDateString()) {
                        $fail('Match date cannot be in the past for upcoming fixtures.');
                    }
                },
            ],
            'venue' => 'required|string|max:255',
            'home_or_away' => 'required|in:home,away',
            'status' => 'required|in:upcoming,finished,cancelled',
            'score_mbuni' => 'nullable|integer|min:0',
            'score_opponent' => 'nullable|integer|min:0',
        ]);

        $data = $request->all();

        // If status is upcoming, clear scores
        if ($request->status === 'upcoming') {
            $data['score_mbuni'] = null;
            $data['score_opponent'] = null;
        }

        Fixture::create($data);

        return redirect()->route('admin.fixtures.index')
            ->with('success', 'Fixture created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Fixture $fixture)
    {
        return view('admin.fixtures.show', compact('fixture'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Fixture $fixture)
    {
        return view('admin.fixtures.edit', compact('fixture'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Fixture $fixture)
    {
        $request->validate([
            'opponent' => 'required|string|max:255',
            'competition' => 'required|string|max:255',
            'match_date' => [
                'required',
                'date',
                function ($attribute, $value, $fail) use ($request, $fixture) {
                    if ($request->status === 'upcoming' && $value < now()->toDateString() && $fixture->status !== 'upcoming') {
                        $fail('Match date cannot be in the past for upcoming fixtures.');
                    }
                },
            ],
            'venue' => 'required|string|max:255',
            'home_or_away' => 'required|in:home,away',
            'status' => 'required|in:upcoming,finished,cancelled',
            'score_mbuni' => 'nullable|integer|min:0',
            'score_opponent' => 'nullable|integer|min:0',
        ]);

        $data = $request->all();

        // If status is upcoming, clear scores
        if ($request->status === 'upcoming') {
            $data['score_mbuni'] = null;
            $data['score_opponent'] = null;
        }

        $fixture->update($data);

        return redirect()->route('admin.fixtures.index')
            ->with('success', 'Fixture updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Fixture $fixture)
    {
        $fixture->delete();

        return redirect()->route('admin.fixtures.index')
            ->with('success', 'Fixture deleted successfully.');
    }
}
