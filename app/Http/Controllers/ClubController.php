<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use App\Models\TimelineEvent;
use App\Services\MetricsService;

class ClubController extends Controller
{
    /**
     * Show the about page
     */
    public function index()
    {
        $siteSettings = Setting::getMultiple([
            'site_name',
            'site_description',
            'about_description',
            'founded_year',
            'total_trophies',
            'about_overview_title',
            'about_overview_content',
            'about_overview_image',
            'mission_statement',
            'vision_statement',
            'values_statement',
            'stadium_name',
            'stadium_capacity',
            'stadium_location',
            'stadium_description',
            'stadium_standard',
            'stadium_image',
            'registered_fans',
            'contact_address',
            'league_titles',
            'cup_victories',
            'community_awards',
            'total_matches_played',
            'total_victories',
            'total_goals_scored',
            'total_clean_sheets'
        ]);

        // Add auto-derived metrics
        $siteSettings['total_players'] = MetricsService::getTotalPlayers();
        $siteSettings['season_progress'] = MetricsService::getSeasonProgress();
        $siteSettings['win_rate'] = MetricsService::getWinRate();

        return view('club.index', compact('siteSettings'));
    }

    /**
     * Show the history page
     */
    public function history()
    {
        $siteSettings = Setting::getMultiple([
            'site_name',
            'founded_year',
            'total_matches_played',
            'total_victories',
            'total_goals_scored',
            'total_clean_sheets',
            'mission_statement',
            'vision_statement',
            'values_statement'
        ]);

        // Get timeline events
        $timelineEvents = TimelineEvent::active()->ordered()->get();

        return view('club.history', compact('siteSettings', 'timelineEvents'));
    }
}
