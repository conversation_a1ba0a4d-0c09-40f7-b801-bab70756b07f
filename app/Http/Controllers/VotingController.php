<?php

namespace App\Http\Controllers;

use App\Models\MotmCandidate;
use App\Models\MotmVote;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class VotingController extends Controller
{
    public function getCandidates()
    {
        $candidates = MotmCandidate::with('player')
            ->active()
            ->get()
            ->map(function ($candidate) {
                return [
                    'id' => $candidate->id,
                    'player' => [
                        'id' => $candidate->player->id,
                        'name' => $candidate->player->name,
                        'photo_url' => $candidate->player->photo_url,
                        'position_name' => $candidate->player->position_name,
                    ],
                    'match_description' => $candidate->match_description,
                    'match_date' => $candidate->match_date->format('M d, Y'),
                    'vote_count' => $candidate->vote_count,
                    'vote_percentage' => $candidate->vote_percentage,
                ];
            });

        return response()->json($candidates);
    }

    public function vote(Request $request)
    {
        $request->validate([
            'candidate_id' => 'required|exists:motm_candidates,id',
        ]);

        // Generate or get voter cookie
        $voterCookie = $request->cookie('motm_voter_id') ?? Str::uuid()->toString();

        // Check if user has already voted
        if (MotmVote::hasVoted($voterCookie)) {
            return response()->json([
                'success' => false,
                'message' => 'You have already voted!'
            ], 400);
        }

        // Check if candidate is active and not expired
        $candidate = MotmCandidate::where('id', $request->candidate_id)
            ->active()
            ->first();

        if (!$candidate) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid candidate or voting is closed.'
            ], 400);
        }

        // Record the vote
        MotmVote::create([
            'motm_candidate_id' => $request->candidate_id,
            'voter_ip' => $request->ip(),
            'voter_cookie' => $voterCookie,
        ]);

        // Set cookie for 3 hours
        $cookie = cookie('motm_voter_id', $voterCookie, 60 * 3);

        return response()->json([
            'success' => true,
            'message' => 'Vote recorded successfully!'
        ])->cookie($cookie);
    }

    public function hasVoted(Request $request)
    {
        $voterCookie = $request->cookie('motm_voter_id');

        return response()->json([
            'has_voted' => $voterCookie ? MotmVote::hasVoted($voterCookie) : false
        ]);
    }
}
