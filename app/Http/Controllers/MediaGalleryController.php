<?php

namespace App\Http\Controllers;

use App\Models\MediaGallery;
use Illuminate\Http\Request;

class MediaGalleryController extends Controller
{
    public function index(Request $request)
    {
        $query = MediaGallery::with('uploader');

        // Filter by file type if provided
        if ($request->has('type') && $request->type) {
            if ($request->type === 'images') {
                $query->images();
            } elseif ($request->type === 'videos') {
                $query->videos();
            }
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('caption', 'like', '%' . $request->search . '%');
            });
        }

        $media = $query->latest()->paginate(20);

        // Get counts for filter tabs
        $totalCount = MediaGallery::count();
        $imageCount = MediaGallery::images()->count();
        $videoCount = MediaGallery::videos()->count();

        return view('media.index', compact(
            'media',
            'totalCount',
            'imageCount',
            'videoCount'
        ))->with('mediaItems', $media);
    }
}
