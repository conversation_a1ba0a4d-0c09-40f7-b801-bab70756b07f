<?php

namespace App\Http\Controllers;

use App\Models\News;
use App\Models\Fixture;
use App\Models\Sponsor;
use App\Services\MetricsService;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        $latestNews = News::published()
            ->with('author')
            ->latest('published_at')
            ->take(3)
            ->get();

        $nextFixture = Fixture::upcoming()->first();

        $recentResults = Fixture::finished()->take(3)->get();

        $sponsors = Sponsor::all();

        return view('home', compact(
            'latestNews',
            'nextFixture',
            'recentResults',
            'sponsors'
        ));
    }
}
