<?php

namespace App\Observers;

use App\Models\News;

class NewsObserver
{
    /**
     * Handle the News "created" event.
     */
    public function created(News $news): void
    {
        // Cache functionality removed
    }

    /**
     * Handle the News "updated" event.
     */
    public function updated(News $news): void
    {
        // Cache functionality removed
    }

    /**
     * Handle the News "deleted" event.
     */
    public function deleted(News $news): void
    {
        // Cache functionality removed
    }

    /**
     * Handle the News "restored" event.
     */
    public function restored(News $news): void
    {
        // Cache functionality removed
    }

    /**
     * Handle the News "force deleted" event.
     */
    public function forceDeleted(News $news): void
    {
        // Cache functionality removed
    }
}
