<?php

namespace App\Services;

use App\Models\Fixture;
use App\Models\Player;
use App\Models\News;
use App\Models\NewsCategory;
use App\Models\Merchandise;

class MetricsService
{
    /**
     * Get auto-derived metrics for the site
     */
    public static function getAutoMetrics(): array
    {
        return [
            // Fixture-based metrics
            'total_wins' => self::getTotalWins(),
            'win_rate' => self::getWinRate(),
            'season_progress' => self::getSeasonProgress(),
            
            // Player-based metrics
            'total_players' => self::getTotalPlayers(),
            'average_age' => self::getAverageAge(),
            'total_nationalities' => self::getTotalNationalities(),
            
            // News-based metrics
            'total_articles' => self::getTotalArticles(),
            'total_reports' => self::getTotalReports(),
            'total_categories' => self::getTotalNewsCategories(),
            'total_readers' => self::getTotalReaders(),
            
            // Store-based metrics
            'store_categories' => self::getStoreCategories(),
        ];
    }

    /**
     * Get total wins from fixtures
     */
    public static function getTotalWins(): int
    {
        return Fixture::where('status', 'finished')
            ->where(function ($query) {
                $query->where(function ($q) {
                    // Home wins - our score is higher than opponent's
                    $q->where('home_or_away', 'home')
                      ->whereColumn('score_mbuni', '>', 'score_opponent');
                })->orWhere(function ($q) {
                    // Away wins - our score is higher than opponent's
                    $q->where('home_or_away', 'away')
                      ->whereColumn('score_mbuni', '>', 'score_opponent');
                });
            })
            ->whereNotNull('score_mbuni')
            ->whereNotNull('score_opponent')
            ->count();
    }

    /**
     * Get win rate percentage
     */
    public static function getWinRate(): int
    {
        $totalMatches = Fixture::where('status', 'finished')
            ->whereNotNull('score_mbuni')
            ->whereNotNull('score_opponent')
            ->count();

        if ($totalMatches === 0) return 0;

        $wins = self::getTotalWins();
        return round(($wins / $totalMatches) * 100);
    }

    /**
     * Get season progress percentage
     */
    public static function getSeasonProgress(): int
    {
        // Get settings for matches per season
        $settings = \App\Models\Setting::pluck('value', 'key')->toArray();
        $totalSeasonMatches = (int) ($settings['matches_per_season'] ?? 38);

        // Get current season matches
        $currentYear = date('Y');
        $playedMatches = Fixture::whereYear('match_date', $currentYear)
            ->where('status', 'finished')
            ->count();

        if ($totalSeasonMatches === 0) return 0;

        return min(100, round(($playedMatches / $totalSeasonMatches) * 100));
    }

    /**
     * Get total active players
     */
    public static function getTotalPlayers(): int
    {
        return Player::count();
    }

    /**
     * Get average age of players
     */
    public static function getAverageAge(): int
    {
        $averageAge = Player::whereNotNull('age')
            ->avg('age');
            
        return $averageAge ? round($averageAge) : 24;
    }

    /**
     * Get total nationalities
     */
    public static function getTotalNationalities(): int
    {
        return Player::whereNotNull('nationality')
            ->distinct('nationality')
            ->count('nationality');
    }

    /**
     * Get total news articles
     */

    public static function getTotalArticles(): int
    {
        // ids of categories whose name or slug contains "report"
        $reportCategoryIds = NewsCategory::where('name', 'like', '%report%')
            ->orWhere('slug', 'like', '%report%')
            ->pluck('id')
            ->toArray();

        return News::where('status', 'published')
            ->where(function ($q) use ($reportCategoryIds) {
                // exclude news with a category_id in reportCategoryIds
                if (!empty($reportCategoryIds)) {
                    $q->whereNotIn('category_id', $reportCategoryIds);
                }
                // keep news with no category (null)
                $q->orWhereNull('category_id');
            })
            ->count();
    }

    public static function getTotalReports(): int
    {
        $reportCategoryIds = NewsCategory::where('name', 'like', '%report%')
            ->orWhere('slug', 'like', '%report%')
            ->pluck('id')
            ->toArray();

        return News::where('status', 'published')
            ->when(!empty($reportCategoryIds), fn($q) => $q->whereIn('category_id', $reportCategoryIds))
            ->count();
    }


    /**
     * Get total news categories
     */
    public static function getTotalNewsCategories(): int
    {
        return NewsCategory::active()->count();
    }

    /**
     * Get total readers (from cookie tracking)
     */
    public static function getTotalReaders(): int
    {
        // Get total unique readers from view counts
        $totalViews = News::sum('views') ?? 0;

        // Return at least 500 if no views yet (for demo purposes)
        return max($totalViews, 0);
    }

    /**
     * Get store categories count
     */
    public static function getStoreCategories(): int
    {
        return Merchandise::whereNotNull('category')
            ->distinct('category')
            ->count('category');
    }

    /**
     * Merge auto metrics with manual settings
     */
    public static function mergeWithSettings(array $settings): array
    {
        $autoMetrics = self::getAutoMetrics();
        
        // Override manual settings with auto-derived values
        return array_merge($settings, $autoMetrics);
    }
}
