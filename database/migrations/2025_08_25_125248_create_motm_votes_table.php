<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('motm_votes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('motm_candidate_id')->constrained()->onDelete('cascade');
            $table->string('voter_ip');
            $table->string('voter_cookie');
            $table->timestamps();

            $table->index(['voter_ip', 'voter_cookie']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('motm_votes');
    }
};
