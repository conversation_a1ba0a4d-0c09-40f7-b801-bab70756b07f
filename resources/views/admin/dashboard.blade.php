@extends('admin.layouts.app')

@section('title', 'Dashboard')
@section('breadcrumb', 'Admin Panel / Dashboard')

@section('content')
<div class="space-y-8">
    <!-- Welcome Section -->
    <div class="admin-card p-8 border border-white/10 mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-3xl font-bold text-white mb-2">Welcome back, {{ auth()->user()->name }}! 👋</h2>
                <p class="text-gray-400">Here's what's happening with Mbuni FC today.</p>
            </div>
            <div class="text-right">
                <p class="text-sm text-gray-400">{{ now()->format('l, F j, Y') }}</p>
                <!-- <p class="text-lg font-semibold text-white">{{ now()->format('g:i A') }}</p> -->
            </div>
        </div>
    </div>

    <!-- Enhanced Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- News Stats -->
        <div class="admin-card p-6 border border-blue-500/20 hover:border-blue-500/40 transition-all duration-300 group">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                    </svg>
                </div>
                <div class="text-right">
                    <div class="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center">
                        <span class="text-blue-400 text-sm font-bold">📰</span>
                    </div>
                </div>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-400 mb-1">Total News</p>
                <p class="text-3xl font-bold text-white mb-2 counter" data-target="{{ $stats['total_news'] ?? 0 }}">0</p>
                <div class="flex items-center">
                    <span class="text-xs text-gray-400 bg-green-500/20 px-2 py-1 rounded-full">
                        {{ $stats['published_news'] ?? 0 }} published
                    </span>
                </div>
            </div>
        </div>

        <!-- Players Stats -->
        <div class="admin-card p-6 border border-green-500/20 hover:border-green-500/40 transition-all duration-300 group">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div class="text-right">
                    <div class="w-12 h-12 bg-green-500/20 rounded-full flex items-center justify-center">
                        <span class="text-green-400 text-sm font-bold">👥</span>
                    </div>
                </div>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-400 mb-1">Total Players</p>
                <p class="text-3xl font-bold text-white mb-2 counter" data-target="{{ $stats['total_players'] ?? 0 }}">0</p>
                <div class="flex items-center">
                    <span class="text-xs text-gray-400 bg-blue-500/20 px-2 py-1 rounded-full">
                        Squad members
                    </span>
                </div>
            </div>
        </div>

        <!-- Fixtures Stats -->
        <div class="admin-card p-6 border border-yellow-500/20 hover:border-yellow-500/40 transition-all duration-300 group">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="text-right">
                    <div class="w-12 h-12 bg-yellow-500/20 rounded-full flex items-center justify-center">
                        <span class="text-yellow-400 text-sm font-bold">📅</span>
                    </div>
                </div>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-400 mb-1">Upcoming Fixtures</p>
                <p class="text-3xl font-bold text-white mb-2 counter" data-target="{{ $stats['upcoming_fixtures'] ?? 0 }}">0</p>
                <div class="flex items-center">
                    <span class="text-xs text-gray-400 bg-purple-500/20 px-2 py-1 rounded-full">
                        Next matches
                    </span>
                </div>
            </div>
        </div>

        <!-- Media Stats -->
        <div class="admin-card p-6 border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 group">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="text-right">
                    <div class="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center">
                        <span class="text-purple-400 text-sm font-bold">🎬</span>
                    </div>
                </div>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-400 mb-1">Media Files</p>
                <p class="text-3xl font-bold text-white mb-2 counter" data-target="{{ $stats['total_media'] ?? 0 }}">0</p>
                <div class="flex items-center">
                    <span class="text-xs text-gray-400 bg-orange-500/20 px-2 py-1 rounded-full">
                        Images & videos
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Performance Chart -->
        

        <!-- Activity Feed -->
        <!-- <div class="admin-card p-6 border border-white/10">
            <h3 class="text-xl font-bold text-white mb-6">Recent Activity</h3>
            <div class="space-y-4 max-h-64 overflow-y-auto">
                <div class="flex items-center p-3 bg-white/5 rounded-lg">
                    <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <p class="text-white text-sm">New article published</p>
                        <p class="text-gray-400 text-xs">2 minutes ago</p>
                    </div>
                </div>

                <div class="flex items-center p-3 bg-white/5 rounded-lg">
                    <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <p class="text-white text-sm">Player profile updated</p>
                        <p class="text-gray-400 text-xs">15 minutes ago</p>
                    </div>
                </div>

                <div class="flex items-center p-3 bg-white/5 rounded-lg">
                    <div class="w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <p class="text-white text-sm">Fixture scheduled</p>
                        <p class="text-gray-400 text-xs">1 hour ago</p>
                    </div>
                </div>

                <div class="flex items-center p-3 bg-white/5 rounded-lg">
                    <div class="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <p class="text-white text-sm">Media uploaded</p>
                        <p class="text-gray-400 text-xs">3 hours ago</p>
                    </div>
                </div>
            </div>
        </div> -->
    </div>

    <!-- Recent Activities -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent News -->
        <div class="admin-card rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-white">Recent News</h3>
            </div>
            <div class="p-6">
                @if($recentNews->count() > 0)
                    <div class="space-y-4">
                        @foreach($recentNews as $news)
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-sm font-medium text-white">{{ Str::limit($news->title, 40) }}</h4>
                                    <p class="text-xs text-gray-500">By {{ $news->author->name }} • {{ $news->created_at->diffForHumans() }}</p>
                                </div>
                                <span class="px-2 py-1 text-xs rounded-full {{ $news->status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                    {{ ucfirst($news->status) }}
                                </span>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 text-sm">No news articles yet.</p>
                @endif
            </div>
        </div>

        <!-- Upcoming Fixtures -->
        <div class="bg-white rounded-lg shadow admin-card">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-white">Upcoming Fixtures</h3>
            </div>
            <div class="p-6">
                @if($upcomingFixtures->count() > 0)
                    <div class="space-y-4">
                        @foreach($upcomingFixtures as $fixture)
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-sm font-medium text-white">vs {{ $fixture->opponent }}</h4>
                                    <p class="text-xs text-gray-500">{{ $fixture->match_date->format('M d, Y') }} • {{ $fixture->stadium }}</p>
                                </div>
                                <span class="text-xs text-gray-500">
                                    {{ $fixture->match_date->format('H:i') }}
                                </span>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 text-sm">No upcoming fixtures.</p>
                @endif
            </div>
        </div>
    </div>


</div>

<script>
// Counter Animation
function animateCounters() {
    const counters = document.querySelectorAll('.counter');

    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = 2000; // 2 seconds
        const increment = target / (duration / 16); // 60fps
        let current = 0;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.textContent = Math.floor(current);
        }, 16);
    });
}

// Chart Animation
function animateCharts() {
    const chartBars = document.querySelectorAll('.chart-bar');
    chartBars.forEach((bar, index) => {
        setTimeout(() => {
            bar.style.transform = 'scaleY(1)';
        }, index * 100);
    });
}

// Initialize animations when page loads
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        animateCounters();
        animateCharts();
    }, 500);
});
</script>
@endsection
