@extends('admin.layouts.app')

@section('title', 'Edit Media')

@section('content')
<div class="space-y-6">
    <!-- Header -->

    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-900">Edit Media</h1>>
        <div class="space-x-2">
            <a href="{{ route('admin.media.index') }}" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors">
                Back to Media
            </a>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="admin-card p-8">
        <form action="{{ route('admin.media.update', $medium) }}" method="POST" class="space-y-6">
            @csrf
            @method('PUT')

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Media Preview -->
                <div>
                    <h2 class="text-lg font-semibold text-white mb-4">Current Media</h2>
                    
                    <div class="bg-admin-surface-light rounded-lg overflow-hidden mb-4">
                        @if($medium->file_type === 'image')
                            <img src="{{ $medium->full_file_url }}"
                                 alt="{{ $medium->title }}"
                                 class="w-full h-auto max-h-64 object-contain">
                        @else
                            @if(str_contains($medium->file_url, 'youtube') || str_contains($medium->file_url, 'youtu.be'))
                                @php
                                    $videoId = null;
                                    if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/', $medium->file_url, $matches)) {
                                        $videoId = $matches[1];
                                    }
                                @endphp
                                @if($videoId)
                                    <div class="aspect-video">
                                        <iframe src="https://www.youtube.com/embed/{{ $videoId }}"
                                                class="w-full h-full"
                                                frameborder="0"
                                                allowfullscreen></iframe>
                                    </div>
                                @endif
                            @else
                                <video controls class="w-full h-auto max-h-64" preload="metadata">
                                    <source src="{{ $medium->full_file_url }}" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>
                            @endif
                        @endif
                    </div>

                    <div class="text-sm text-white">
                        <p><span class="font-medium">Current Type:</span> {{ ucfirst($medium->file_type) }}</p>
                        <p><span class="font-medium">Current URL:</span>
                            <a href="{{ $medium->full_file_url }}" target="_blank" class="text-admin-accent hover:underline break-all">
                                {{ Str::limit($medium->full_file_url, 50) }}
                            </a>
                        </p>
                    </div>
                </div>

                <!-- Edit Form Fields -->
                <div class="space-y-6">
                    <h2 class="text-lg font-semibold text-white mb-4">Media Information</h2>

                    <!-- Title -->
                    <div>
                        <label for="title" class="block text-sm font-medium text-white mb-2">Title</label>
                        <input type="text" 
                               name="title" 
                               id="title" 
                               value="{{ old('title', $medium->title) }}"
                               class="admin-input @error('title') border-red-500 @enderror" 
                               required>
                        @error('title')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- File Type -->
                    <div>
                        <label for="file_type" class="block text-sm font-medium text-white mb-2">File Type</label>
                        <select name="file_type" 
                                id="file_type" 
                                class="admin-select @error('file_type') border-red-500 @enderror" 
                                required>
                            <option value="image" {{ old('file_type', $medium->file_type) === 'image' ? 'selected' : '' }}>📸 Image</option>
                            <option value="video" {{ old('file_type', $medium->file_type) === 'video' ? 'selected' : '' }}>🎥 Video</option>
                        </select>
                        @error('file_type')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- File URL -->
                    <div>
                        <label for="file_url" class="block text-sm font-medium text-white mb-2">File URL</label>
                        <input type="url" 
                               name="file_url" 
                               id="file_url" 
                               value="{{ old('file_url', $medium->file_url) }}"
                               class="admin-input @error('file_url') border-red-500 @enderror" 
                               required
                               placeholder="https://example.com/image.jpg or YouTube URL">
                        @error('file_url')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-white">
                            Enter direct file URL or YouTube/video platform URL
                        </p>
                    </div>

                    <!-- Caption -->
                    <div>
                        <label for="caption" class="block text-sm font-medium text-white mb-2">Caption</label>
                        <textarea name="caption" 
                                  id="caption" 
                                  rows="4" 
                                  class="admin-textarea @error('caption') border-red-500 @enderror"
                                  placeholder="Optional caption or description for the media...">{{ old('caption', $medium->caption) }}</textarea>
                        @error('caption')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Additional Information -->
                    <div class="bg-admin-surface-light p-4 rounded-lg">
                        <h3 class="text-sm font-semibold text-white mb-2">Additional Information</h3>
                        <div class="space-y-2 text-sm text-white">
                            @if($medium->uploader)
                                <div><span class="font-medium">Uploaded by:</span> {{ $medium->uploader->name }}</div>
                            @endif
                            <div><span class="font-medium">Upload date:</span> {{ $medium->created_at->format('M j, Y g:i A') }}</div>
                            <div><span class="font-medium">Last updated:</span> {{ $medium->updated_at->format('M j, Y g:i A') }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-admin-border">
                <a href="{{ route('admin.media.index') }}" class="admin-btn-secondary">Cancel</a>
                <button type="submit" class="admin-btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Update Media
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Preview URL changes
document.getElementById('file_url').addEventListener('input', function() {
    const url = this.value;
    const fileType = document.getElementById('file_type').value;
    
    // You could add URL validation or preview functionality here
    if (url && fileType === 'image' && (url.includes('.jpg') || url.includes('.png') || url.includes('.gif'))) {
        // Could show preview of new image
    }
});

document.getElementById('file_type').addEventListener('change', function() {
    const fileUrlInput = document.getElementById('file_url');
    const fileType = this.value;
    
    if (fileType === 'image') {
        fileUrlInput.placeholder = 'https://example.com/image.jpg';
    } else {
        fileUrlInput.placeholder = 'https://youtube.com/watch?v=... or direct video URL';
    }
});
</script>
@endsection
