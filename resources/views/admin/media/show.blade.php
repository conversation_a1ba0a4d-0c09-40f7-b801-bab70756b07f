@extends('admin.layouts.app')

@section('title', 'View Media')

@section('content')
<div class="space-y-6">
    <!-- Header -->

    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-900">View Media</h1>
        <div class="space-x-2">
            <a href="{{ route('admin.media.edit', $medium) }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                Edit Media
            </a>
            <a href="{{ route('admin.media.index') }}" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors">
                Back to Media
            </a>
        </div>
    </div>

    <!-- Media Details Card -->
    <div class="admin-card p-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Media Preview -->
            <div>
                <h2 class="text-xl font-semibold text-white mb-4">Media Preview</h2>
                
                <div class="bg-admin-surface-light rounded-lg overflow-hidden">
                    @if($medium->file_type === 'image')
                        <img src="{{ $medium->full_file_url }}"
                             alt="{{ $medium->title }}"
                             class="w-full h-auto max-h-96 object-contain">
                    @else
                        @if(str_contains($medium->file_url, 'youtube') || str_contains($medium->file_url, 'youtu.be'))
                            @php
                                $videoId = null;
                                if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/', $medium->file_url, $matches)) {
                                    $videoId = $matches[1];
                                }
                            @endphp
                            @if($videoId)
                                <div class="aspect-video">
                                    <iframe src="https://www.youtube.com/embed/{{ $videoId }}"
                                            class="w-full h-full"
                                            frameborder="0"
                                            allowfullscreen></iframe>
                                </div>
                            @endif
                        @else
                            <video controls class="w-full h-auto max-h-96" preload="metadata">
                                <source src="{{ $medium->full_file_url }}" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                        @endif
                    @endif
                </div>

                <!-- File Information -->
                <div class="mt-4 p-4 bg-admin-surface-light rounded-lg">
                    <h3 class="text-sm font-semibold text-white mb-2">File Information</h3>
                    <div class="space-y-1 text-sm text-white">
                        <div><span class="font-medium">Type:</span> {{ ucfirst($medium->file_type) }}</div>
                        <div><span class="font-medium">URL:</span> 
                            <a href="{{ $medium->file_url_full }}" target="_blank" class="text-admin-accent hover:underline break-all">
                                {{ $medium->file_url_full }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Media Information -->
            <div class="space-y-6">
                <h2 class="text-xl font-semibold text-white mb-4">Media Information</h2>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-white mb-1">Title</label>
                        <p class="text-white text-lg font-semibold">{{ $medium->title }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white mb-1">Type</label>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                            {{ $medium->file_type === 'image' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800' }}">
                            @if($medium->file_type === 'image')
                                📸 Image
                            @else
                                🎥 Video
                            @endif
                        </span>
                    </div>

                    @if($medium->caption)
                        <div>
                            <label class="block text-sm font-medium text-white mb-1">Caption</label>
                            <div class="bg-admin-surface-light p-4 rounded-lg">
                                <p class="text-white leading-relaxed">{{ $medium->caption }}</p>
                            </div>
                        </div>
                    @endif

                    @if($medium->uploader)
                        <div>
                            <label class="block text-sm font-medium text-white mb-1">Uploaded By:</label>
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-admin-primary rounded-full flex items-center justify-center">
                                    <!-- <span class="text-white text-sm font-semibold">
                                        {{ substr($medium->uploader->name, 0, 1) }}
                                    </span> -->
                                </div>
                                <div>
                                    <p class="text-white font-medium">{{ $medium->uploader->name }}</p>
                                    <p class="text-white text-sm">{{ $medium->uploader->email }}</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Usage Statistics -->
                    <!-- <div>
                        <label class="block text-sm font-medium text-white mb-1">Usage Statistics</label>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="bg-admin-surface-light p-3 rounded-lg text-center">
                                <div class="text-lg font-bold text-admin-accent">{{ rand(50, 500) }}</div>
                                <div class="text-xs text-white">Views</div>
                            </div>
                            <div class="bg-admin-surface-light p-3 rounded-lg text-center">
                                <div class="text-lg font-bold text-admin-accent">{{ rand(5, 50) }}</div>
                                <div class="text-xs text-white">Shares</div>
                            </div>
                        </div>
                    </div> -->
                </div>
            </div>
        </div>

        <!-- Timestamps -->
        <div class="mt-8 pt-6 border-t border-admin-border">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-white">
                <div>
                    <span class="font-medium">Uploaded:</span> {{ $medium->created_at->format('M j, Y g:i A') }}
                </div>
                <div>
                    <span class="font-medium">Last Updated:</span> {{ $medium->updated_at->format('M j, Y g:i A') }}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
