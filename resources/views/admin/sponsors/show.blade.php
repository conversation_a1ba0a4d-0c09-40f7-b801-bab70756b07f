@extends('admin.layouts.app')

@section('title', 'View Sponsor')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-900">View Sponsor</h1>
        <div class="space-x-2">
            <a href="{{ route('admin.sponsors.edit', $sponsor) }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                Edit Sponsor
            </a>
            <a href="{{ route('admin.sponsors.index') }}" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors">
                Back to Sponsors
            </a>
        </div>
    </div>

    <!-- Sponsor Details Card -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="p-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Sponsor Logo -->
                <div class="space-y-4">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Logo</h2>
                    @if($sponsor->logo)
                        <div class="bg-gray-50 border-2 border-gray-200 rounded-lg p-8 flex items-center justify-center" style="min-height: 300px;">
                            <img src="{{ $sponsor->logo_url }}" 
                                 alt="{{ $sponsor->name }}" 
                                 class="max-w-full max-h-72 object-contain">
                        </div>
                        <div class="text-sm text-gray-500 text-center">
                            Click <a href="{{ $sponsor->logo_url }}" target="_blank" class="text-blue-600 hover:text-blue-800">here</a> to view full size
                        </div>
                    @else
                        <div class="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-8 flex items-center justify-center" style="min-height: 300px;">
                            <div class="text-center text-gray-400">
                                <svg class="mx-auto h-16 w-16 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <p class="text-lg font-medium">No Logo Available</p>
                                <p class="text-sm">Upload a logo in the edit section</p>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Sponsor Information -->
                <div class="space-y-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Sponsor Information</h2>
                    
                    <div class="space-y-4">
                        <!-- Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">Sponsor Name</label>
                            <p class="text-2xl font-bold text-gray-900">{{ $sponsor->name }}</p>
                        </div>

                        <!-- Website -->
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">Website</label>
                            @if($sponsor->website_url)
                                <div class="flex items-center space-x-2">
                                    <a href="{{ $sponsor->website_url }}" 
                                       target="_blank" 
                                       class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                        </svg>
                                        {{ $sponsor->website_url }}
                                    </a>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        Active Link
                                    </span>
                                </div>
                                <p class="text-sm text-gray-500 mt-1">Click to visit sponsor's website</p>
                            @else
                                <div class="flex items-center space-x-2">
                                    <span class="text-gray-500">No website provided</span>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                        No Website
                                    </span>
                                </div>
                            @endif
                        </div>

                        <!-- Logo Status -->
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">Logo Status</label>
                            @if($sponsor->logo)
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Logo Available
                                </span>
                            @else
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                    No Logo
                                </span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Sponsor Stats -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="font-medium text-gray-900 mb-2">Sponsor Details</h3>
                        <div class="space-y-1 text-sm text-gray-600">
                            <p><span class="font-medium">ID:</span> #{{ str_pad($sponsor->id, 4, '0', STR_PAD_LEFT) }}</p>
                            <p><span class="font-medium">Status:</span> 
                                <span class="text-green-600 font-medium">Active</span>
                            </p>
                        </div>
                    </div>

                    <!-- Website Status -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="font-medium text-gray-900 mb-2">Online Presence</h3>
                        @if($sponsor->website_url)
                            <p class="text-sm text-green-600">✓ Website link available</p>
                            <p class="text-sm text-gray-600">Ready for public display</p>
                        @else
                            <p class="text-sm text-yellow-600">⚠️ No website provided</p>
                            <p class="text-sm text-gray-600">Consider adding website URL</p>
                        @endif
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="font-medium text-gray-900 mb-2">Quick Actions</h3>
                        <div class="space-y-2">
                            <a href="{{ route('admin.sponsors.edit', $sponsor) }}" 
                               class="block text-sm text-blue-600 hover:text-blue-800">
                                📝 Edit Sponsor Details
                            </a>
                            @if($sponsor->website_url)
                                <a href="{{ $sponsor->website_url }}" 
                                   target="_blank"
                                   class="block text-sm text-green-600 hover:text-green-800">
                                    🔗 Visit Website
                                </a>
                            @endif
                            <form action="{{ route('admin.sponsors.destroy', $sponsor) }}" method="POST" class="inline"
                                  onsubmit="return confirm('Are you sure you want to delete this sponsor? This action cannot be undone.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="text-sm text-red-600 hover:text-red-800">
                                    🗑️ Delete Sponsor
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Timestamps -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-500">
                    <div>
                        <span class="font-medium">Created:</span> {{ $sponsor->created_at->format('M j, Y g:i A') }}
                    </div>
                    <div>
                        <span class="font-medium">Last Updated:</span> {{ $sponsor->updated_at->format('M j, Y g:i A') }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Usage Information -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Sponsor Display Information</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <p>This sponsor {{ $sponsor->logo ? 'is ready for display' : 'needs a logo before being displayed' }} on the website. 
                    {{ $sponsor->website_url ? 'Visitors can click to visit their website.' : 'Consider adding a website URL for better engagement.' }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection