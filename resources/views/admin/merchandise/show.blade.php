@extends('admin.layouts.app')

@section('title', 'View Product')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-900">View Product</h1>
        <div class="space-x-2">
            <a href="{{ route('admin.merchandise.edit', $merchandise) }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                Edit Product
            </a>
            <a href="{{ route('admin.merchandise.index') }}" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors">
                Back to Products
            </a>
        </div>
    </div>

    <!-- Product Details Card -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="p-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Product Image -->
                <div class="space-y-4">
                    @if($merchandise->image)
                        <div class="aspect-square rounded-lg overflow-hidden bg-gray-100">
                            <img src="{{ $merchandise->image_url }}" 
                                 alt="{{ $merchandise->name }}" 
                                 class="w-full h-full object-cover">
                        </div>
                    @else
                        <div class="aspect-square rounded-lg bg-gray-100 flex items-center justify-center">
                            <div class="text-center text-gray-400">
                                <svg class="mx-auto h-16 w-16 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <p class="text-lg font-medium">No Image Available</p>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Product Information -->
                <div class="space-y-6">
                    <h2 class="text-3xl font-bold text-gray-900">{{ $merchandise->name }}</h2>
                    
                    <div class="space-y-4">
                        <!-- Price -->
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">Price</label>
                            <p class="text-3xl font-bold text-green-600">Tsh.{{ number_format($merchandise->price, 2) }}</p>
                        </div>

                        <!-- Category -->
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">Category</label>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                {{ ucfirst($merchandise->category) }}
                            </span>
                        </div>

                        <!-- Stock Status -->
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">Stock Status</label>
                            <div class="flex items-center space-x-3">
                                @if($merchandise->stock_quantity <= 0)
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                        Out of Stock
                                    </span>
                                @elseif($merchandise->stock_quantity <= 5)
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                        </svg>
                                        Low Stock
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        In Stock
                                    </span>
                                @endif
                                <span class="text-lg font-semibold text-gray-900">{{ $merchandise->stock_quantity }} units</span>
                            </div>
                        </div>

                        <!-- Description -->
                        @if($merchandise->description)
                            <div>
                                <label class="block text-sm font-medium text-gray-600 mb-2">Description</label>
                                <div class="prose prose-sm text-gray-700 bg-gray-50 p-4 rounded-lg">
                                    {{ $merchandise->description }}
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Quick Stats -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="font-medium text-gray-900 mb-2">Quick Stats</h3>
                        <div class="space-y-1 text-sm text-gray-600">
                            <p><span class="font-medium">SKU:</span> #{{ str_pad($merchandise->id, 6, '0', STR_PAD_LEFT) }}</p>
                            <p><span class="font-medium">Total Value:</span> ${{ number_format($merchandise->price * $merchandise->stock_quantity, 2) }}</p>
                        </div>
                    </div>

                    <!-- Inventory Alert -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="font-medium text-gray-900 mb-2">Inventory Alert</h3>
                        @if($merchandise->stock_quantity <= 0)
                            <p class="text-sm text-red-600">⚠️ Product is out of stock</p>
                        @elseif($merchandise->stock_quantity <= 5)
                            <p class="text-sm text-yellow-600">⚠️ Low stock - consider restocking</p>
                        @else
                            <p class="text-sm text-green-600">✓ Stock levels are healthy</p>
                        @endif
                    </div>

                    <!-- Actions -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="font-medium text-gray-900 mb-2">Quick Actions</h3>
                        <div class="space-y-2">
                            <a href="{{ route('admin.merchandise.edit', $merchandise) }}" 
                               class="block text-sm text-blue-600 hover:text-blue-800">
                                📝 Edit Product Details
                            </a>
                            <form action="{{ route('admin.merchandise.destroy', $merchandise) }}" method="POST" class="inline"
                                  onsubmit="return confirm('Are you sure you want to delete this product? This action cannot be undone.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="text-sm text-red-600 hover:text-red-800">
                                    🗑️ Delete Product
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Timestamps -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-500">
                    <div>
                        <span class="font-medium">Created:</span> {{ $merchandise->created_at->format('M j, Y g:i A') }}
                    </div>
                    <div>
                        <span class="font-medium">Last Updated:</span> {{ $merchandise->updated_at->format('M j, Y g:i A') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection