<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="dark">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }} - Admin Panel</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Enhanced Admin Styles -->
    <style>
        /* Dark Theme Variables */
        :root {
            --admin-bg: #0f0f23;
            --admin-surface: #1a1a2e;
            --admin-surface-light: #16213e;
            --admin-primary: #dc2626;
            --admin-primary-light: #ef4444;
            --admin-accent: #fbbf24;
            --admin-text: #f8fafc;
            --admin-text-muted: #94a3b8;
            --admin-border: #334155;
        }

        /* Glassmorphism Sidebar */
        .admin-sidebar {
            background: linear-gradient(135deg, rgba(15, 15, 35, 0.95) 0%, rgba(26, 26, 46, 0.95) 100%);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .admin-sidebar.collapsed {
            width: 80px;
        }

        .admin-sidebar.collapsed .nav-text,
        .admin-sidebar.collapsed .user-info {
            opacity: 0;
            pointer-events: none;
        }

        /* Mobile sidebar */
        @media (max-width: 768px) {
            .admin-sidebar {
                position: fixed;
                left: -100%;
                z-index: 50;
                height: 100vh;
                transition: left 0.3s ease;
            }

            .admin-sidebar.mobile-open {
                left: 0;
            }

            .admin-sidebar.collapsed {
                width: 256px; /* Reset width on mobile */
            }
        }

        /* Toggle button for collapsed sidebar */
        .sidebar-toggle-collapsed {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 60;
            background: var(--admin-surface);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 12px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .sidebar-toggle-collapsed:hover {
            background: var(--admin-surface-light);
            transform: scale(1.05);
        }

        /* Neumorphism Cards */
        .admin-card {
            background: var(--admin-surface);
            border-radius: 20px;
            box-shadow:
                20px 20px 60px rgba(0, 0, 0, 0.3),
                -20px -20px 60px rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .admin-card:hover {
            transform: translateY(-5px);
            box-shadow:
                25px 25px 80px rgba(0, 0, 0, 0.4),
                -25px -25px 80px rgba(255, 255, 255, 0.08);
        }

        /* Sidebar Links */
        .sidebar-link {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .sidebar-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .sidebar-link:hover::before {
            left: 100%;
        }

        .sidebar-link.active {
            background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-light) 100%);
            color: white;
            box-shadow: 0 8px 32px rgba(220, 38, 38, 0.3);
        }

        /* Toggle Switch */
        .toggle-switch {
            position: relative;
            width: 60px;
            height: 30px;
            background: var(--admin-surface-light);
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: inset 5px 5px 10px rgba(0, 0, 0, 0.3), inset -5px -5px 10px rgba(255, 255, 255, 0.05);
        }

        .toggle-switch.active {
            background: var(--admin-primary);
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3);
        }

        .toggle-switch.active::after {
            transform: translateX(30px);
        }

        /* Loading Skeleton */
        .skeleton {
            background: linear-gradient(90deg, var(--admin-surface) 25%, var(--admin-surface-light) 50%, var(--admin-surface) 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* Animated Charts */
        .chart-container {
            position: relative;
            overflow: hidden;
        }

        .chart-bar {
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            transform-origin: bottom;
        }

        /* Collapsing Animation */
        .collapse-enter {
            opacity: 0;
            transform: translateX(-20px);
        }

        .collapse-enter-active {
            opacity: 1;
            transform: translateX(0);
            transition: all 0.3s ease;
        }

        .collapse-exit {
            opacity: 1;
            transform: translateX(0);
        }

        .collapse-exit-active {
            opacity: 0;
            transform: translateX(-20px);
            transition: all 0.3s ease;
        }
    </style>
</head>
<body class="font-inter antialiased bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen">
    <div class="min-h-screen flex relative">
        <!-- Animated Background -->
        <div class="fixed inset-0 overflow-hidden pointer-events-none">
            <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-red-500/10 rounded-full blur-3xl animate-pulse"></div>
            <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-yellow-500/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>
        </div>

        <!-- Mobile Overlay -->
        <div id="mobile-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden md:hidden"></div>

        <!-- Toggle Button for Collapsed/Mobile Sidebar -->
        <button id="sidebar-toggle-collapsed" class="sidebar-toggle-collapsed hidden">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>

        <!-- Enhanced Sidebar -->
        <div id="sidebar" class="admin-sidebar w-64 text-white flex flex-col relative z-10">
            <!-- Logo Section -->
            <div class="p-6 border-b border-white/10">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mr-3">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <div id="logo-text" class="transition-opacity duration-300">
                            <h1 class="text-xl font-bold text-white">Mbuni FC</h1>
                            <p class="text-xs text-gray-400">Admin Panel</p>
                        </div>
                    </div>
                    <button id="sidebar-toggle" class="p-2 rounded-lg hover:bg-white/10 transition-colors">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Enhanced Navigation -->
            <nav class="flex-1 p-4 overflow-y-auto">
                <div class="space-y-1">
                    <!-- Dashboard -->
                    <div class="nav-item">
                        <a href="{{ route('admin.dashboard') }}"
                           class="sidebar-link group flex items-center px-4 py-3 rounded-xl transition-all duration-300 {{ request()->routeIs('admin.dashboard') ? 'active' : 'hover:bg-white/10' }}">
                            <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 mr-3 group-hover:scale-110 transition-transform">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                                </svg>
                            </div>
                            <span class="nav-text font-medium">Dashboard</span>
                        </a>
                    </div>

                    <!-- News -->
                    <div class="nav-item">
                        <a href="{{ route('admin.news.index') }}"
                           class="sidebar-link group flex items-center px-4 py-3 rounded-xl transition-all duration-300 {{ request()->routeIs('admin.news.*') && !request()->routeIs('admin.news-categories.*') ? 'active' : 'hover:bg-white/10' }}">
                            <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-green-500 to-green-600 mr-3 group-hover:scale-110 transition-transform">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                </svg>
                            </div>
                            <span class="nav-text font-medium">News</span>
                        </a>
                    </div>



                    <!-- Fixtures -->
                    <div class="nav-item">
                        <a href="{{ route('admin.fixtures.index') }}"
                           class="sidebar-link group flex items-center px-4 py-3 rounded-xl transition-all duration-300 {{ request()->routeIs('admin.fixtures.*') ? 'active' : 'hover:bg-white/10' }}">
                            <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-purple-500 to-purple-600 mr-3 group-hover:scale-110 transition-transform">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <span class="nav-text font-medium">Fixtures</span>
                        </a>
                    </div>

                    <!-- Players -->
                    <div class="nav-item">
                        <a href="{{ route('admin.players.index') }}"
                           class="sidebar-link group flex items-center px-4 py-3 rounded-xl transition-all duration-300 {{ request()->routeIs('admin.players.*') ? 'active' : 'hover:bg-white/10' }}">
                            <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-yellow-500 to-yellow-600 mr-3 group-hover:scale-110 transition-transform">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                            </div>
                            <span class="nav-text font-medium">Players</span>
                        </a>
                    </div>

                    <!-- Merchandise -->
                    <div class="nav-item">
                        <a href="{{ route('admin.merchandise.index') }}"
                           class="sidebar-link group flex items-center px-4 py-3 rounded-xl transition-all duration-300 {{ request()->routeIs('admin.merchandise.*') ? 'active' : 'hover:bg-white/10' }}">
                            <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-pink-500 to-pink-600 mr-3 group-hover:scale-110 transition-transform">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                </svg>
                            </div>
                            <span class="nav-text font-medium">Merchandise</span>
                        </a>
                    </div>
                    <!-- Media -->
                    <div class="nav-item">
                        <a href="{{ route('admin.media.index') }}"
                           class="sidebar-link group flex items-center px-4 py-3 rounded-xl transition-all duration-300 {{ request()->routeIs('admin.media.*') ? 'active' : 'hover:bg-white/10' }}">
                            <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-indigo-500 to-indigo-600 mr-3 group-hover:scale-110 transition-transform">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <span class="nav-text font-medium">Media</span>
                        </a>
                    </div>

                    <!-- Sponsors -->
                    <div class="nav-item">
                        <a href="{{ route('admin.sponsors.index') }}"
                           class="sidebar-link group flex items-center px-4 py-3 rounded-xl transition-all duration-300 {{ request()->routeIs('admin.sponsors.*') ? 'active' : 'hover:bg-white/10' }}">
                            <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-orange-500 to-orange-600 mr-3 group-hover:scale-110 transition-transform">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                            </div>
                            <span class="nav-text font-medium">Sponsors</span>
                        </a>
                    </div>

                    <!-- Man of the Match -->
                    <div class="nav-item">
                        <a href="{{ route('admin.motm.index') }}"
                           class="sidebar-link group flex items-center px-4 py-3 rounded-xl transition-all duration-300 {{ request()->routeIs('admin.motm.*') ? 'active' : 'hover:bg-white/10' }}">
                            <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-yellow-500 to-yellow-600 mr-3 group-hover:scale-110 transition-transform">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                </svg>
                            </div>
                            <span class="nav-text font-medium">MOTM Voting</span>
                        </a>
                    </div>

                    <!-- Contact Messages -->
                    <div class="nav-item">
                        <a href="{{ route('admin.contact-messages.index') }}"
                           class="sidebar-link group flex items-center px-4 py-3 rounded-xl transition-all duration-300 {{ request()->routeIs('admin.contact-messages.*') ? 'active' : 'hover:bg-white/10' }}">
                            <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-indigo-500 to-indigo-600 mr-3 group-hover:scale-110 transition-transform">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <span class="nav-text font-medium">Messages</span>
                        </a>
                    </div>

                    <!-- Divider -->
                    <div class="my-4 border-t border-white/10"></div>

                    <!-- Settings -->
                    <div class="nav-item">
                        <a href="{{ route('admin.settings.index') }}"
                           class="sidebar-link group flex items-center px-4 py-3 rounded-xl transition-all duration-300 {{ request()->routeIs('admin.settings.*') ? 'active' : 'hover:bg-white/10' }}">
                            <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-gray-500 to-gray-600 mr-3 group-hover:scale-110 transition-transform">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <span class="nav-text font-medium">Settings</span>
                        </a>
                    </div>
                </div>
            </nav>

            <!-- Enhanced User Info -->
            <div class="p-4 border-t border-white/10">
                <div class="admin-card p-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-lg">
                            {{ substr(auth()->user()->name, 0, 1) }}
                        </div>
                        <div class="ml-3 flex-1 user-info min-w-0">
                            <p class="text-sm font-semibold text-white truncate">{{ auth()->user()->name }}</p>
                            <p class="text-xs text-gray-400 capitalize truncate">{{ auth()->user()->role ?? 'Administrator' }}</p>
                        </div>
                        <!-- <div class="ml-2">
                            <div class="toggle-switch" onclick="toggleTheme()" title="Toggle Theme">
                                <div class="toggle-indicator"></div>
                            </div>
                        </div> -->
                    </div>
                    <div class="mt-4 space-y-2">
                        <a href="{{ route('admin.profile.edit') }}" class="w-full flex items-center justify-center px-4 py-2 bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 hover:text-blue-300 rounded-lg transition-all duration-300 text-sm font-medium">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <span class="nav-text">Profile</span>
                        </a>

                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="w-full flex items-center justify-center px-4 py-2 bg-red-500/20 hover:bg-red-500/30 text-red-400 hover:text-red-300 rounded-lg transition-all duration-300 text-sm font-medium">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                                <span class="nav-text">Logout</span>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Main Content -->
        <div class="flex-1 flex flex-col relative z-10">
            <!-- Enhanced Top Bar -->
            <header class="admin-card m-4 mb-0 border border-white/10">
                <div class="px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <!-- Mobile Menu Button -->
                            <button id="mobile-menu-toggle" class="md:hidden mr-4 p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                                </svg>
                            </button>
                            <h1 class="text-2xl font-bold text-white mr-4">
                                @yield('title', 'Dashboard')
                            </h1>
                            <div class="breadcrumb text-sm text-gray-400">
                                @yield('breadcrumb', 'Admin Panel')
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            <!-- Notifications -->
                            <!-- <button class="relative p-2 rounded-lg hover:bg-white/10 transition-colors">
                                <svg class="w-6 h-6 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.5a6 6 0 0 1 6 6v2l1.5 3h-15l1.5-3v-2a6 6 0 0 1 6-6z"></path>
                                </svg>
                                <span class="absolute top-1 right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></span>
                            </button> -->

                            <!-- View Site -->
                            <a href="{{ route('home') }}" target="_blank"
                               class="flex items-center px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-300 text-sm font-medium">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                                View Site
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Enhanced Page Content -->
            <main class="flex-1 p-6 overflow-y-auto">
                <div class="max-w-7xl mx-auto">
                    <!-- Enhanced Flash Messages -->
                    @if (session('success'))
                        <div class="mb-6 admin-card border border-green-500/20 bg-green-500/10">
                            <div class="p-4 flex items-center">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                                <span class="text-white font-medium">{{ session('success') }}</span>
                            </div>
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="mb-6 admin-card border border-red-500/20 bg-red-500/10">
                            <div class="p-4 flex items-center">
                                <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center mr-3">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </div>
                                <span class="text-red-400 font-medium">{{ session('error') }}</span>
                            </div>
                        </div>
                    @endif

                    @yield('content')
                </div>
            </main>
        </div>
    </div>

    <!-- Enhanced Scripts -->
    <script>
        // Enhanced Sidebar Toggle Functionality
        const sidebar = document.getElementById('sidebar');
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const sidebarToggleCollapsed = document.getElementById('sidebar-toggle-collapsed');
        const mobileOverlay = document.getElementById('mobile-overlay');
        const logoText = document.getElementById('logo-text');
        const navTexts = document.querySelectorAll('.nav-text');
        const userInfo = document.querySelector('.user-info');

        function toggleSidebar() {
            const isMobile = window.innerWidth < 768;

            if (isMobile) {
                // Mobile behavior
                sidebar.classList.toggle('mobile-open');
                mobileOverlay.classList.toggle('hidden');
                document.body.style.overflow = sidebar.classList.contains('mobile-open') ? 'hidden' : 'auto';
            } else {
                // Desktop behavior
                sidebar.classList.toggle('collapsed');

                if (sidebar.classList.contains('collapsed')) {
                    sidebarToggleCollapsed.classList.remove('hidden');
                    logoText.style.opacity = '0';
                    navTexts.forEach(text => text.style.opacity = '0');
                    if (userInfo) userInfo.style.opacity = '0';
                } else {
                    sidebarToggleCollapsed.classList.add('hidden');
                    logoText.style.opacity = '1';
                    navTexts.forEach(text => text.style.opacity = '1');
                    if (userInfo) userInfo.style.opacity = '1';
                }
            }
        }

        // Event listeners
        sidebarToggle.addEventListener('click', toggleSidebar);
        sidebarToggleCollapsed.addEventListener('click', toggleSidebar);

        // Mobile menu toggle
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', toggleSidebar);
        }

        // Close mobile sidebar when clicking overlay
        mobileOverlay.addEventListener('click', function() {
            sidebar.classList.remove('mobile-open');
            mobileOverlay.classList.add('hidden');
            document.body.style.overflow = 'auto';
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            const isMobile = window.innerWidth < 768;

            if (!isMobile) {
                // Reset mobile states when switching to desktop
                sidebar.classList.remove('mobile-open');
                mobileOverlay.classList.add('hidden');
                document.body.style.overflow = 'auto';

                // Show/hide collapsed toggle based on sidebar state
                if (sidebar.classList.contains('collapsed')) {
                    sidebarToggleCollapsed.classList.remove('hidden');
                } else {
                    sidebarToggleCollapsed.classList.add('hidden');
                }
            } else {
                // Hide collapsed toggle on mobile
                sidebarToggleCollapsed.classList.add('hidden');
                sidebar.classList.remove('collapsed');

                // Reset text opacity on mobile
                logoText.style.opacity = '1';
                navTexts.forEach(text => text.style.opacity = '1');
                if (userInfo) userInfo.style.opacity = '1';
            }
        });

        // Theme Toggle Functionality
        function toggleTheme() {
            const toggle = document.querySelector('.toggle-switch');
            toggle.classList.toggle('active');
        }

        // Initialize animations when page loads
        document.addEventListener('DOMContentLoaded', function() {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateX(-20px)';

                setTimeout(() => {
                    item.style.transition = 'all 0.3s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateX(0)';
                }, index * 50);
            });
        });
    </script>
</body>
</html>
