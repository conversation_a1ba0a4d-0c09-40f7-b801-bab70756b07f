@extends('admin.layouts.app')

@section('title', 'View Category')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Category Details</h1>
            <p class="text-sm text-gray-600">View category information and associated articles</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('admin.news-categories.edit', $newsCategory) }}" class="bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700 transition-colors">
                Edit Category
            </a>
            <a href="{{ route('admin.news-categories.index') }}" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors">
                Back to Categories
            </a>
        </div>
    </div>

    <!-- Category Information -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center">
                <div class="w-6 h-6 rounded-full mr-3" style="background-color: {{ $newsCategory->color }};"></div>
                <h3 class="text-lg font-medium text-gray-900">{{ $newsCategory->name }}</h3>
                <span class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $newsCategory->is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                    {{ $newsCategory->is_active ? 'Active' : 'Inactive' }}
                </span>
            </div>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Basic Information -->
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Name</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $newsCategory->name }}</p>
                    </div>
                    
                    @if($newsCategory->description)
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Description</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $newsCategory->description }}</p>
                    </div>
                    @endif
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Color</label>
                        <div class="mt-1 flex items-center space-x-2">
                            <div class="w-6 h-6 rounded-full border border-gray-300" style="background-color: {{ $newsCategory->color }};"></div>
                            <span class="text-sm text-gray-900">{{ $newsCategory->color }}</span>
                        </div>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Total Articles</label>
                        <p class="mt-1 text-2xl font-semibold text-blue-600">{{ $newsCategory->news_count }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Status</label>
                        <p class="mt-1">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $newsCategory->is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                {{ $newsCategory->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Created</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $newsCategory->created_at->format('M d, Y \a\t H:i') }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Last Updated</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $newsCategory->updated_at->format('M d, Y \a\t H:i') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Associated Articles -->
    @if($newsCategory->news_count > 0)
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Associated Articles ({{ $newsCategory->news_count }})</h3>
        </div>
        
        <div class="p-6">
            <div class="space-y-4">
                @foreach($newsCategory->news()->latest()->take(10)->get() as $article)
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div class="flex-1">
                        <h4 class="text-sm font-medium text-gray-900">{{ $article->title }}</h4>
                        <p class="text-xs text-gray-500 mt-1">
                            Published {{ $article->published_at ? $article->published_at->format('M d, Y') : 'Draft' }}
                            @if($article->author)
                                by {{ $article->author->name }}
                            @endif
                        </p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $article->status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                            {{ ucfirst($article->status) }}
                        </span>
                        <a href="{{ route('admin.news.edit', $article) }}" class="text-blue-600 hover:text-blue-900 text-sm">
                            Edit
                        </a>
                    </div>
                </div>
                @endforeach
                
                @if($newsCategory->news_count > 10)
                <div class="text-center pt-4">
                    <a href="{{ route('admin.news.index', ['category' => $newsCategory->id]) }}" class="text-blue-600 hover:text-blue-900 text-sm">
                        View all {{ $newsCategory->news_count }} articles →
                    </a>
                </div>
                @endif
            </div>
        </div>
    </div>
    @endif
</div>
@endsection
