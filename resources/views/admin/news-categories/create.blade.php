@extends('admin.layouts.app')

@section('title', 'Create News Category')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Create News Category</h1>
        <a href="{{ route('admin.news-categories.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Categories
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Category Details</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.news-categories.store') }}" method="POST">
                        @csrf

                        <div class="form-group">
                            <label for="name">Category Name *</label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name') }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" 
                                      placeholder="Optional description for this category">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="color">Category Color *</label>
                            <div class="input-group">
                                <input type="color" class="form-control @error('color') is-invalid @enderror" 
                                       id="color" name="color" value="{{ old('color', '#3B82F6') }}" required>
                                <div class="input-group-append">
                                    <span class="input-group-text">
                                        <i class="fas fa-palette"></i>
                                    </span>
                                </div>
                            </div>
                            @error('color')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">This color will be used for category badges and labels.</small>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="is_active" 
                                       name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="is_active">Active</label>
                            </div>
                            <small class="form-text text-muted">Only active categories will be available for selection.</small>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Category
                            </button>
                            <a href="{{ route('admin.news-categories.index') }}" class="btn btn-secondary ml-2">
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Preview</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>Category Badge Preview:</h6>
                        <span class="badge" id="color-preview" style="background-color: #3B82F6; color: white;">
                            <span id="name-preview">Category Name</span>
                        </span>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Category List Preview:</h6>
                        <div class="d-flex align-items-center">
                            <div class="rounded-circle mr-2" id="dot-preview" 
                                 style="width: 20px; height: 20px; background-color: #3B82F6;"></div>
                            <span id="list-name-preview">Category Name</span>
                        </div>
                    </div>

                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        The preview updates as you type and change the color.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const colorInput = document.getElementById('color');
    const namePreview = document.getElementById('name-preview');
    const listNamePreview = document.getElementById('list-name-preview');
    const colorPreview = document.getElementById('color-preview');
    const dotPreview = document.getElementById('dot-preview');

    function updatePreview() {
        const name = nameInput.value || 'Category Name';
        const color = colorInput.value;

        namePreview.textContent = name;
        listNamePreview.textContent = name;
        colorPreview.style.backgroundColor = color;
        dotPreview.style.backgroundColor = color;
    }

    nameInput.addEventListener('input', updatePreview);
    colorInput.addEventListener('input', updatePreview);
});
</script>
@endsection
