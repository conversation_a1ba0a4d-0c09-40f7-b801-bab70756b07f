@extends('admin.layouts.app')

@section('title', 'Edit Fixture')

@section('content')
<div class="space-y-6">
    <!-- Header -->

    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-900">View Fixture</h1>
        <div class="space-x-2">
            <a href="{{ route('admin.fixtures.index') }}" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors">
                Back to Fixtures
            </a>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="admin-card p-8">
        <form action="{{ route('admin.fixtures.update', $fixture) }}" method="POST" class="space-y-6">
            @csrf
            @method('PUT')

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left Column -->
                <div class="space-y-6">
                    <!-- Opponent -->
                    <div>
                        <label for="opponent" class="block text-sm font-medium text-gray-300 mb-2">Opponent Team</label>
                        <input type="text" 
                               name="opponent" 
                               id="opponent" 
                               value="{{ old('opponent', $fixture->opponent) }}"
                               class="admin-input @error('opponent') border-red-500 @enderror" 
                               required>
                        @error('opponent')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Competition -->
                    <div>
                        <label for="competition" class="block text-sm font-medium text-gray-300 mb-2">Competition</label>
                        <input type="text" 
                               name="competition" 
                               id="competition" 
                               value="{{ old('competition', $fixture->competition) }}"
                               class="admin-input @error('competition') border-red-500 @enderror" 
                               required>
                        @error('competition')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Match Date -->
                    <div>
                        <label for="match_date" class="block text-sm font-medium text-gray-300 mb-2">Match Date & Time</label>
                        <input type="datetime-local" 
                               name="match_date" 
                               id="match_date" 
                               value="{{ old('match_date', $fixture->match_date->format('Y-m-d\TH:i')) }}"
                               class="admin-input @error('match_date') border-red-500 @enderror" 
                               required>
                        @error('match_date')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Venue -->
                    <div>
                        <label for="venue" class="block text-sm font-medium text-gray-300 mb-2">Venue</label>
                        <input type="text" 
                               name="venue" 
                               id="venue" 
                               value="{{ old('venue', $fixture->venue) }}"
                               class="admin-input @error('venue') border-red-500 @enderror" 
                               required>
                        @error('venue')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Right Column -->
                <div class="space-y-6">
                    <!-- Home/Away -->
                    <div>
                        <label for="home_or_away" class="block text-sm font-medium text-gray-300 mb-2">Home/Away</label>
                        <select name="home_or_away" 
                                id="home_or_away" 
                                class="admin-select @error('home_or_away') border-red-500 @enderror" 
                                required>
                            <option value="home" {{ old('home_or_away', $fixture->home_or_away) === 'home' ? 'selected' : '' }}>Home</option>
                            <option value="away" {{ old('home_or_away', $fixture->home_or_away) === 'away' ? 'selected' : '' }}>Away</option>
                        </select>
                        @error('home_or_away')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Status -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-300 mb-2">Status</label>
                        <select name="status" 
                                id="status" 
                                class="admin-select @error('status') border-red-500 @enderror" 
                                required>
                            <option value="upcoming" {{ old('status', $fixture->status) === 'upcoming' ? 'selected' : '' }}>Upcoming</option>
                            <option value="finished" {{ old('status', $fixture->status) === 'finished' ? 'selected' : '' }}>Finished</option>
                            <option value="cancelled" {{ old('status', $fixture->status) === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                        </select>
                        @error('status')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Scores (only show if status is finished) -->
                    <div id="scores-section" style="display: {{ old('status', $fixture->status) === 'finished' ? 'block' : 'none' }};">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="score_mbuni" class="block text-sm font-medium text-gray-300 mb-2">Mbuni FC Score</label>
                                <input type="number" 
                                       name="score_mbuni" 
                                       id="score_mbuni" 
                                       value="{{ old('score_mbuni', $fixture->score_mbuni) }}"
                                       class="admin-input @error('score_mbuni') border-red-500 @enderror" 
                                       min="0">
                                @error('score_mbuni')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="score_opponent" class="block text-sm font-medium text-gray-300 mb-2">Opponent Score</label>
                                <input type="number" 
                                       name="score_opponent" 
                                       id="score_opponent" 
                                       value="{{ old('score_opponent', $fixture->score_opponent) }}"
                                       class="admin-input @error('score_opponent') border-red-500 @enderror" 
                                       min="0">
                                @error('score_opponent')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-admin-border">
                <a href="{{ route('admin.fixtures.index') }}" class="admin-btn-secondary">Cancel</a>
                <button type="submit" class="admin-btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Update Fixture
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.getElementById('status').addEventListener('change', function() {
    const scoresSection = document.getElementById('scores-section');
    if (this.value === 'finished') {
        scoresSection.style.display = 'block';
    } else {
        scoresSection.style.display = 'none';
        document.getElementById('score_mbuni').value = '';
        document.getElementById('score_opponent').value = '';
    }
});
</script>
@endsection
