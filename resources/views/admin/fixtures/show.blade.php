@extends('admin.layouts.app')

@section('title', 'View Fixture')

@section('content')
<div class="space-y-6">
    <!-- Header -->

    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-900">View Fixture</h1>
        <div class="space-x-2">
            <a href="{{ route('admin.fixtures.edit', $fixture) }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                Edit Fixture
            </a>
            <a href="{{ route('admin.fixtures.index') }}" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors">
                Back to Fixtures
            </a>
        </div>
    </div>

    <!-- Fixture Details Card -->
    <div class="admin-card p-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Basic Information -->
            <div class="space-y-6">
                <h2 class="text-xl font-semibold text-white mb-4">Match Information</h2>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-400 mb-1">Opponent</label>
                        <p class="text-white text-lg font-semibold">{{ $fixture->opponent }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-400 mb-1">Competition</label>
                        <p class="text-white">{{ $fixture->competition }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-400 mb-1">Match Date</label>
                        <p class="text-white">{{ $fixture->match_date->format('F j, Y \a\t g:i A') }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-400 mb-1">Venue</label>
                        <p class="text-white">{{ $fixture->venue }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-400 mb-1">Home/Away</label>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                            {{ $fixture->home_or_away === 'home' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800' }}">
                            {{ ucfirst($fixture->home_or_away) }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Match Status and Score -->
            <div class="space-y-6">
                <h2 class="text-xl font-semibold text-white mb-4">Match Status</h2>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-400 mb-1">Status</label>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                            @if($fixture->status === 'upcoming') bg-yellow-100 text-yellow-800
                            @elseif($fixture->status === 'finished') bg-green-100 text-green-800
                            @else bg-red-100 text-red-800 @endif">
                            {{ ucfirst($fixture->status) }}
                        </span>
                    </div>

                    @if($fixture->status === 'finished' && ($fixture->score_mbuni !== null || $fixture->score_opponent !== null))
                        <div>
                            <label class="block text-sm font-medium text-gray-400 mb-1">Final Score</label>
                            <div class="bg-admin-surface-light p-4 rounded-lg">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-white">
                                        Mbuni FC {{ $fixture->score_mbuni ?? 0 }} - {{ $fixture->score_opponent ?? 0 }} {{ $fixture->opponent }}
                                    </div>
                                    @if($fixture->score_mbuni > $fixture->score_opponent)
                                        <div class="text-green-400 font-semibold mt-2">Victory!</div>
                                    @elseif($fixture->score_mbuni < $fixture->score_opponent)
                                        <div class="text-red-400 font-semibold mt-2">Defeat</div>
                                    @else
                                        <div class="text-yellow-400 font-semibold mt-2">Draw</div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Timestamps -->
        <div class="mt-8 pt-6 border-t border-admin-border">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-400">
                <div>
                    <span class="font-medium">Created:</span> {{ $fixture->created_at->format('M j, Y g:i A') }}
                </div>
                <div>
                    <span class="font-medium">Last Updated:</span> {{ $fixture->updated_at->format('M j, Y g:i A') }}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
