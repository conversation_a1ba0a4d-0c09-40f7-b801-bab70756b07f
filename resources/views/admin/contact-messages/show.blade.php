@extends('admin.layouts.app')

@section('title', 'Contact Message')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-900">Contact Message</h1>
        <a href="{{ route('admin.contact-messages.index') }}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
            Back to Messages
        </a>
    </div>

    @if(session('success'))
        <div class="bg-green-50 border border-green-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                </div>
            </div>
        </div>
    @endif

    <!-- Message Details -->
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <div class="flex justify-between items-start">
                <div>
                    <h3 class="text-lg font-medium text-gray-900">{{ $contactMessage->subject }}</h3>
                    <p class="text-sm text-gray-600 mt-1">
                        Received on {{ $contactMessage->created_at->format('M d, Y \a\t H:i') }}
                    </p>
                </div>
                <div class="flex items-center space-x-2">
                    @if($contactMessage->is_read)
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            Read
                        </span>
                    @else
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            New
                        </span>
                    @endif
                </div>
            </div>
        </div>
        
        <div class="p-6">
            <!-- Sender Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Sender Information</h4>
                    <div class="space-y-2">
                        <div>
                            <span class="text-sm font-medium text-gray-500">Name:</span>
                            <span class="text-sm text-gray-900 ml-2">{{ $contactMessage->name }}</span>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Email:</span>
                            <a href="mailto:{{ $contactMessage->email }}" class="text-sm text-blue-600 hover:text-blue-800 ml-2">
                                {{ $contactMessage->email }}
                            </a>
                        </div>
                        @if($contactMessage->phone)
                            <div>
                                <span class="text-sm font-medium text-gray-500">Phone:</span>
                                <a href="tel:{{ $contactMessage->phone }}" class="text-sm text-blue-600 hover:text-blue-800 ml-2">
                                    {{ $contactMessage->phone }}
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
                
                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Message Details</h4>
                    <div class="space-y-2">
                        <div>
                            <span class="text-sm font-medium text-gray-500">IP Address:</span>
                            <span class="text-sm text-gray-900 ml-2">{{ $contactMessage->ip_address ?? 'N/A' }}</span>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Received:</span>
                            <span class="text-sm text-gray-900 ml-2">{{ $contactMessage->created_at->diffForHumans() }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Message Content -->
            <div>
                <h4 class="text-sm font-medium text-gray-900 mb-3">Message</h4>
                <div class="bg-gray-50 rounded-lg p-4">
                    <p class="text-gray-900 whitespace-pre-wrap">{{ $contactMessage->message }}</p>
                </div>
            </div>
        </div>
        
        <!-- Actions -->
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between">
            <div class="flex space-x-3">
                <a href="mailto:{{ $contactMessage->email }}?subject=Re: {{ $contactMessage->subject }}" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    Reply via Email
                </a>
                
                @if(!$contactMessage->is_read)
                    <form method="POST" action="{{ route('admin.contact-messages.read', $contactMessage) }}" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Mark as Read
                        </button>
                    </form>
                @else
                    <form method="POST" action="{{ route('admin.contact-messages.unread', $contactMessage) }}" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="inline-flex items-center px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            Mark as Unread
                        </button>
                    </form>
                @endif
            </div>
            
            <form method="POST" action="{{ route('admin.contact-messages.destroy', $contactMessage) }}" 
                  class="inline" 
                  onsubmit="return confirm('Are you sure you want to delete this message?')">
                @csrf
                @method('DELETE')
                <button type="submit" class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Delete Message
                </button>
            </form>
        </div>
    </div>
</div>
@endsection
