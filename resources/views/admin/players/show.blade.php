@extends('admin.layouts.app')

@section('title', 'View Player')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-white">View Player</h1>
            <p class="text-admin-text-muted mt-2">Player profile and statistics</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('admin.players.edit', $player) }}" class="admin-btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit Player
            </a>
            <a href="{{ route('admin.players.index') }}" class="admin-btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Players
            </a>
        </div>
    </div>

    <!-- Player Profile Card -->
    <div class="admin-card p-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Player Photo -->
            <div class="lg:col-span-1">
                <div class="text-center">
                    @if($player->photo)
                        <img src="{{ Storage::url($player->photo) }}" 
                             alt="{{ $player->name }}" 
                             class="w-48 h-48 mx-auto rounded-full object-cover border-4 border-admin-primary">
                    @else
                        <div class="w-48 h-48 mx-auto rounded-full bg-admin-surface-light border-4 border-admin-primary flex items-center justify-center">
                            <svg class="w-24 h-24 text-admin-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                    @endif
                    
                    <div class="mt-4">
                        <h2 class="text-2xl font-bold text-white">{{ $player->name }}</h2>
                        <p class="text-admin-accent text-lg font-semibold">#{{ $player->shirt_number }}</p>
                        <p class="text-admin-text-muted">{{ $player->position_full }}</p>
                    </div>
                </div>
            </div>

            <!-- Player Information -->
            <div class="lg:col-span-2 space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Basic Info -->
                    <div class="space-y-4">
                        <h3 class="text-xl font-semibold text-white mb-4">Player Information</h3>
                        
                        <div>
                            <label class="block text-sm font-medium text-admin-text-muted mb-1">Full Name</label>
                            <p class="text-white">{{ $player->name }}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-admin-text-muted mb-1">Position</label>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                @if($player->position === 'GK') bg-purple-100 text-purple-800
                                @elseif($player->position === 'DF') bg-blue-100 text-blue-800
                                @elseif($player->position === 'MF') bg-green-100 text-green-800
                                @else bg-red-100 text-red-800 @endif">
                                {{ $player->position_full }}
                            </span>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-admin-text-muted mb-1">Shirt Number</label>
                            <p class="text-white text-lg font-bold">#{{ $player->shirt_number }}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-admin-text-muted mb-1">Nationality</label>
                            <p class="text-white">{{ $player->nationality }}</p>
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div class="space-y-4">
                        <h3 class="text-xl font-semibold text-white mb-4">Season Statistics</h3>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <div class="bg-admin-surface-light p-4 rounded-lg text-center">
                                <div class="text-2xl font-bold text-admin-accent">{{ $player->stats['goals'] ?? 0 }}</div>
                                <div class="text-sm text-admin-text-muted">Goals</div>
                            </div>

                            <div class="bg-admin-surface-light p-4 rounded-lg text-center">
                                <div class="text-2xl font-bold text-admin-accent">{{ $player->stats['assists'] ?? 0 }}</div>
                                <div class="text-sm text-admin-text-muted">Assists</div>
                            </div>

                            <div class="bg-admin-surface-light p-4 rounded-lg text-center">
                                <div class="text-2xl font-bold text-admin-accent">{{ $player->stats['appearances'] ?? 0 }}</div>
                                <div class="text-sm text-admin-text-muted">Appearances</div>
                            </div>

                            <div class="bg-admin-surface-light p-4 rounded-lg text-center">
                                <div class="text-2xl font-bold text-yellow-400">{{ $player->stats['yellow_cards'] ?? 0 }}</div>
                                <div class="text-sm text-admin-text-muted">Yellow Cards</div>
                            </div>

                            <div class="bg-admin-surface-light p-4 rounded-lg text-center">
                                <div class="text-2xl font-bold text-red-400">{{ $player->stats['red_cards'] ?? 0 }}</div>
                                <div class="text-sm text-admin-text-muted">Red Cards</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Biography -->
                @if($player->bio)
                    <div class="mt-6">
                        <h3 class="text-xl font-semibold text-white mb-4">Biography</h3>
                        <div class="bg-admin-surface-light p-4 rounded-lg">
                            <p class="text-admin-text-muted leading-relaxed">{{ $player->bio }}</p>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Timestamps -->
        <div class="mt-8 pt-6 border-t border-admin-border">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-admin-text-muted">
                <div>
                    <span class="font-medium">Added:</span> {{ $player->created_at->format('M j, Y g:i A') }}
                </div>
                <div>
                    <span class="font-medium">Last Updated:</span> {{ $player->updated_at->format('M j, Y g:i A') }}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
