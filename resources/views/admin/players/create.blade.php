@extends('admin.layouts.app')

@section('title', 'Add New Player')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-white">Add New Player</h1>
            <p class="text-admin-text-muted mt-2">Add a new player to the squad</p>
        </div>
        <a href="{{ route('admin.players.index') }}" class="admin-btn-secondary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Players
        </a>
    </div>

    <!-- Create Form -->
    <div class="admin-card p-8">
        <form action="{{ route('admin.players.store') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
            @csrf

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Photo Upload -->
                <div class="lg:col-span-1">
                    <div class="text-center">
                        <div class="mb-4">
                            <div id="photo-preview" class="w-48 h-48 mx-auto rounded-full bg-admin-surface-light border-4 border-admin-primary flex items-center justify-center">
                                <svg class="w-24 h-24 text-admin-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                        </div>

                        <div>
                            <label for="photo" class="block text-sm font-medium text-admin-text-muted mb-2">Player Photo</label>
                            <input type="file" 
                                   name="photo" 
                                   id="photo" 
                                   accept="image/*"
                                   class="admin-file-input @error('photo') border-red-500 @enderror">
                            @error('photo')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-xs text-admin-text-muted">JPG, PNG, GIF up to 2MB</p>
                        </div>
                    </div>
                </div>

                <!-- Player Information -->
                <div class="lg:col-span-2 space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Basic Information -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-semibold text-white">Basic Information</h3>

                            <!-- Name -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-admin-text-muted mb-2">Full Name</label>
                                <input type="text" 
                                       name="name" 
                                       id="name" 
                                       value="{{ old('name') }}"
                                       class="admin-input @error('name') border-red-500 @enderror" 
                                       required>
                                @error('name')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Position -->
                            <div>
                                <label for="position" class="block text-sm font-medium text-admin-text-muted mb-2">Position</label>
                                <select name="position" 
                                        id="position" 
                                        class="admin-select @error('position') border-red-500 @enderror" 
                                        required>
                                    <option value="">Select Position</option>
                                    <option value="GK" {{ old('position') === 'GK' ? 'selected' : '' }}>Goalkeeper (GK)</option>
                                    <option value="DF" {{ old('position') === 'DF' ? 'selected' : '' }}>Defender (DF)</option>
                                    <option value="MF" {{ old('position') === 'MF' ? 'selected' : '' }}>Midfielder (MF)</option>
                                    <option value="FW" {{ old('position') === 'FW' ? 'selected' : '' }}>Forward (FW)</option>
                                </select>
                                @error('position')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Shirt Number -->
                            <div>
                                <label for="shirt_number" class="block text-sm font-medium text-admin-text-muted mb-2">Shirt Number</label>
                                <input type="number" 
                                       name="shirt_number" 
                                       id="shirt_number" 
                                       value="{{ old('shirt_number') }}"
                                       class="admin-input @error('shirt_number') border-red-500 @enderror" 
                                       min="1" 
                                       max="99" 
                                       required>
                                @error('shirt_number')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Nationality -->
                            <div>
                                <label for="nationality" class="block text-sm font-medium text-admin-text-muted mb-2">Nationality</label>
                                <input type="text"
                                       name="nationality"
                                       id="nationality"
                                       value="{{ old('nationality') }}"
                                       class="admin-input @error('nationality') border-red-500 @enderror"
                                       required>
                                @error('nationality')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Age -->
                            <div>
                                <label for="age" class="block text-sm font-medium text-admin-text-muted mb-2">Age</label>
                                <input type="number"
                                       name="age"
                                       id="age"
                                       value="{{ old('age') }}"
                                       class="admin-input @error('age') border-red-500 @enderror"
                                       min="16"
                                       max="45">
                                @error('age')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Statistics -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-semibold text-white">Season Statistics</h3>

                            <!-- Goals -->
                            <div>
                                <label for="goals" class="block text-sm font-medium text-admin-text-muted mb-2">Goals</label>
                                <input type="number" 
                                       name="goals" 
                                       id="goals" 
                                       value="{{ old('goals', 0) }}"
                                       class="admin-input @error('goals') border-red-500 @enderror" 
                                       min="0">
                                @error('goals')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Assists -->
                            <div>
                                <label for="assists" class="block text-sm font-medium text-admin-text-muted mb-2">Assists</label>
                                <input type="number" 
                                       name="assists" 
                                       id="assists" 
                                       value="{{ old('assists', 0) }}"
                                       class="admin-input @error('assists') border-red-500 @enderror" 
                                       min="0">
                                @error('assists')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Appearances -->
                            <div>
                                <label for="appearances" class="block text-sm font-medium text-admin-text-muted mb-2">Appearances</label>
                                <input type="number" 
                                       name="appearances" 
                                       id="appearances" 
                                       value="{{ old('appearances', 0) }}"
                                       class="admin-input @error('appearances') border-red-500 @enderror" 
                                       min="0">
                                @error('appearances')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <!-- Yellow Cards -->
                                <div>
                                    <label for="yellow_cards" class="block text-sm font-medium text-admin-text-muted mb-2">Yellow Cards</label>
                                    <input type="number" 
                                           name="yellow_cards" 
                                           id="yellow_cards" 
                                           value="{{ old('yellow_cards', 0) }}"
                                           class="admin-input @error('yellow_cards') border-red-500 @enderror" 
                                           min="0">
                                    @error('yellow_cards')
                                        <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Red Cards -->
                                <div>
                                    <label for="red_cards" class="block text-sm font-medium text-admin-text-muted mb-2">Red Cards</label>
                                    <input type="number" 
                                           name="red_cards" 
                                           id="red_cards" 
                                           value="{{ old('red_cards', 0) }}"
                                           class="admin-input @error('red_cards') border-red-500 @enderror" 
                                           min="0">
                                    @error('red_cards')
                                        <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Social Media Links -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <h3 class="text-lg font-semibold text-white">Social Media</h3>

                            <!-- Instagram -->
                            <div>
                                <label for="instagram" class="block text-sm font-medium text-admin-text-muted mb-2">Instagram</label>
                                <input type="url"
                                       name="social_media[instagram]"
                                       id="instagram"
                                       value="{{ old('social_media.instagram') }}"
                                       class="admin-input @error('social_media.instagram') border-red-500 @enderror"
                                       placeholder="https://instagram.com/username">
                                @error('social_media.instagram')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Twitter -->
                            <div>
                                <label for="twitter" class="block text-sm font-medium text-admin-text-muted mb-2">Twitter</label>
                                <input type="url"
                                       name="social_media[twitter]"
                                       id="twitter"
                                       value="{{ old('social_media.twitter') }}"
                                       class="admin-input @error('social_media.twitter') border-red-500 @enderror"
                                       placeholder="https://twitter.com/username">
                                @error('social_media.twitter')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Facebook -->
                            <div>
                                <label for="facebook" class="block text-sm font-medium text-admin-text-muted mb-2">Facebook</label>
                                <input type="url"
                                       name="social_media[facebook]"
                                       id="facebook"
                                       value="{{ old('social_media.facebook') }}"
                                       class="admin-input @error('social_media.facebook') border-red-500 @enderror"
                                       placeholder="https://facebook.com/username">
                                @error('social_media.facebook')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Previous Clubs -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-semibold text-white">Previous Clubs</h3>

                            <!-- Club 1 -->
                            <div>
                                <label for="club1_name" class="block text-sm font-medium text-admin-text-muted mb-2">Club 1 Name</label>
                                <input type="text"
                                       name="previous_clubs[0][name]"
                                       id="club1_name"
                                       value="{{ old('previous_clubs.0.name') }}"
                                       class="admin-input @error('previous_clubs.0.name') border-red-500 @enderror"
                                       placeholder="Previous club name">
                                @error('previous_clubs.0.name')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Club 2 -->
                            <div>
                                <label for="club2_name" class="block text-sm font-medium text-admin-text-muted mb-2">Club 2 Name</label>
                                <input type="text"
                                       name="previous_clubs[1][name]"
                                       id="club2_name"
                                       value="{{ old('previous_clubs.1.name') }}"
                                       class="admin-input @error('previous_clubs.1.name') border-red-500 @enderror"
                                       placeholder="Previous club name">
                                @error('previous_clubs.1.name')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Club 3 -->
                            <div>
                                <label for="club3_name" class="block text-sm font-medium text-admin-text-muted mb-2">Club 3 Name</label>
                                <input type="text"
                                       name="previous_clubs[2][name]"
                                       id="club3_name"
                                       value="{{ old('previous_clubs.2.name') }}"
                                       class="admin-input @error('previous_clubs.2.name') border-red-500 @enderror"
                                       placeholder="Previous club name">
                                @error('previous_clubs.2.name')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Biography -->
                    <div>
                        <label for="bio" class="block text-sm font-medium text-admin-text-muted mb-2">Biography</label>
                        <textarea name="bio"
                                  id="bio"
                                  rows="4"
                                  class="admin-textarea @error('bio') border-red-500 @enderror"
                                  placeholder="Player biography, background, achievements...">{{ old('bio') }}</textarea>
                        @error('bio')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-admin-border">
                <a href="{{ route('admin.players.index') }}" class="admin-btn-secondary">Cancel</a>
                <button type="submit" class="admin-btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Add Player
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.getElementById('photo').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('photo-preview');
            preview.innerHTML = `<img src="${e.target.result}" class="w-48 h-48 mx-auto rounded-full object-cover border-4 border-admin-primary">`;
        };
        reader.readAsDataURL(file);
    }
});
</script>
@endsection
