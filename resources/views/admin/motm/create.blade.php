@extends('admin.layouts.app')

@section('title', 'Set MOTM Candidates')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-900">Set Man of the Match Candidates</h1>
        <a href="{{ route('admin.motm.index') }}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
            Back to Voting
        </a>
    </div>

    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Select Candidates</h3>
            <p class="text-sm text-gray-600">Choose up to 3 players for the Man of the Match voting. This will deactivate any current voting.</p>
        </div>
        
        <form method="POST" action="{{ route('admin.motm.store') }}" class="p-6">
            @csrf
            
            <div class="space-y-6">
                <!-- Match Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Match Description</label>
                        <input type="text" name="match_description" value="{{ old('match_description') }}" 
                               placeholder="e.g., vs Simba SC - League Match"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
                        @error('match_description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Match Date</label>
                        <input type="date" name="match_date" value="{{ old('match_date', date('Y-m-d')) }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
                        @error('match_date')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Voting Expiration -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Voting Expires After (Hours)</label>
                    <select name="expiration_hours" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <option value="3" {{ old('expiration_hours') == '3' ? 'selected' : '' }}>3 Hours</option>
                        <option value="6" {{ old('expiration_hours') == '6' ? 'selected' : '' }}>6 Hours</option>
                        <option value="12" {{ old('expiration_hours') == '12' ? 'selected' : '' }}>12 Hours</option>
                        <option value="24" {{ old('expiration_hours') == '24' ? 'selected' : '' }}>24 Hours</option>
                        <option value="48" {{ old('expiration_hours') == '48' ? 'selected' : '' }}>48 Hours</option>
                        <option value="72" {{ old('expiration_hours') == '72' ? 'selected' : '' }}>72 Hours</option>
                        <option value="" {{ old('expiration_hours') == '' ? 'selected' : '' }}>Never Expires</option>
                    </select>
                    @error('expiration_hours')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-xs text-gray-500">Set when the voting should automatically close</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                </div>

                <!-- Player Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Select Players (1-3 players)</label>
                    @error('player_ids')
                        <p class="mb-3 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto border border-gray-200 rounded-lg p-4">
                        @foreach($players as $player)
                            <div class="relative">
                                <input type="checkbox" name="player_ids[]" value="{{ $player->id }}" 
                                       id="player_{{ $player->id }}" 
                                       class="sr-only peer"
                                       {{ in_array($player->id, old('player_ids', [])) ? 'checked' : '' }}>
                                
                                <label for="player_{{ $player->id }}" 
                                       class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 peer-checked:bg-blue-50 peer-checked:border-blue-500 transition-all">
                                    <img src="{{ $player->photo_url }}" 
                                         alt="{{ $player->name }}" 
                                         class="w-12 h-12 rounded-full object-cover mr-3">
                                    <div class="flex-1">
                                        <div class="font-medium text-gray-900">{{ $player->name }}</div>
                                        <div class="text-sm text-gray-500">{{ $player->position_name }}</div>
                                        <div class="text-xs text-gray-400">Jersey #{{ $player->jersey_number ?? 'N/A' }}</div>
                                    </div>
                                    <div class="ml-2">
                                        <svg class="w-5 h-5 text-blue-600 opacity-0 peer-checked:opacity-100 transition-opacity" 
                                             fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                </label>
                            </div>
                        @endforeach
                    </div>
                    
                    <p class="mt-2 text-sm text-gray-500">
                        <span id="selected-count">0</span> of 3 players selected
                    </p>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end space-x-3">
                    <a href="{{ route('admin.motm.index') }}" 
                       class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                        Set Candidates & Start Voting
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('input[name="player_ids[]"]');
    const selectedCount = document.getElementById('selected-count');
    const submitButton = document.querySelector('button[type="submit"]');
    
    function updateSelectedCount() {
        const checked = document.querySelectorAll('input[name="player_ids[]"]:checked');
        selectedCount.textContent = checked.length;
        
        // Disable submit button if no players selected
        submitButton.disabled = checked.length === 0;
        if (checked.length === 0) {
            submitButton.classList.add('opacity-50', 'cursor-not-allowed');
        } else {
            submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
        }
        
        // Disable other checkboxes if 3 are selected
        checkboxes.forEach(checkbox => {
            if (!checkbox.checked && checked.length >= 3) {
                checkbox.disabled = true;
                checkbox.parentElement.classList.add('opacity-50', 'cursor-not-allowed');
            } else {
                checkbox.disabled = false;
                checkbox.parentElement.classList.remove('opacity-50', 'cursor-not-allowed');
            }
        });
    }
    
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });
    
    // Initial count
    updateSelectedCount();
});
</script>
@endsection
