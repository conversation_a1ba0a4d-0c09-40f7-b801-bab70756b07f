@extends('admin.layouts.app')

@section('title', 'Man of the Match Voting')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-900">Man of the Match Voting</h1>
        <a href="{{ route('admin.motm.create') }}" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
            Set New Candidates
        </a>
    </div>

    @if(session('success'))
        <div class="bg-green-50 border border-green-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                </div>
            </div>
        </div>
    @endif

    <!-- Current Active Candidates -->
    @php
        $activeCandidates = $candidates->where('is_active', true);
        $totalVotes = $activeCandidates->sum('vote_count');
    @endphp

    @if($activeCandidates->count() > 0)
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Current Voting</h3>
                <p class="text-sm text-gray-600">Active candidates for Man of the Match ({{ $totalVotes }} total votes)</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    @foreach($activeCandidates as $candidate)
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex items-center mb-3">
                                <img src="{{ $candidate->player->photo_url }}" 
                                     alt="{{ $candidate->player->name }}" 
                                     class="w-12 h-12 rounded-full object-cover mr-3">
                                <div>
                                    <h4 class="font-semibold text-gray-900">{{ $candidate->player->name }}</h4>
                                    <p class="text-sm text-gray-600">{{ $candidate->player->position_name }}</p>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="flex justify-between text-sm mb-1">
                                    <span>Votes</span>
                                    <span class="font-semibold">{{ $candidate->vote_count }} ({{ $candidate->vote_percentage }}%)</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $candidate->vote_percentage }}%"></div>
                                </div>
                            </div>

                            <p class="text-xs text-gray-500 mb-3">{{ $candidate->match_description }}</p>
                            <p class="text-xs text-gray-400">{{ $candidate->match_date->format('M d, Y') }}</p>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @else
        <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-yellow-800">No active voting</p>
                    <p class="text-sm text-yellow-700 mt-1">Set up candidates for the next Man of the Match voting.</p>
                </div>
            </div>
        </div>
    @endif

    <!-- All Candidates History -->
    @if($candidates->count() > 0)
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Voting History</h3>
                <p class="text-sm text-gray-600">All past and current candidates</p>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Player</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Match</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Votes</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($candidates as $candidate)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <img src="{{ $candidate->player->photo_url }}" 
                                             alt="{{ $candidate->player->name }}" 
                                             class="w-8 h-8 rounded-full object-cover mr-3">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ $candidate->player->name }}</div>
                                            <div class="text-sm text-gray-500">{{ $candidate->player->position_name }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $candidate->match_description }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ $candidate->match_date->format('M d, Y') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $candidate->vote_count }} votes ({{ $candidate->vote_percentage }}%)
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($candidate->is_active)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Active
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            Inactive
                                        </span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <form method="POST" action="{{ route('admin.motm.toggle', $candidate) }}" class="inline">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit" class="text-blue-600 hover:text-blue-900">
                                                {{ $candidate->is_active ? 'Deactivate' : 'Activate' }}
                                            </button>
                                        </form>
                                        
                                        <form method="POST" action="{{ route('admin.motm.destroy', $candidate) }}" 
                                              class="inline" 
                                              onsubmit="return confirm('Are you sure you want to delete this candidate?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="text-red-600 hover:text-red-900">
                                                Delete
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    @endif
</div>
@endsection
