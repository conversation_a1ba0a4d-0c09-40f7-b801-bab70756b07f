@extends('admin.layouts.app')

@section('title', 'Site Settings')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-900">Site Settings</h1>
        <button type="submit" form="settings-form" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
            Save Settings
        </button>
    </div>

    @if(session('success'))
        <div class="bg-green-50 border border-green-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                </div>
            </div>
        </div>
    @endif

    <form id="settings-form" action="{{ route('admin.settings.update') }}" method="POST" enctype="multipart/form-data">

        @csrf
        @method('PUT')

        <!-- Settings Sections -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">

            <!-- General Settings -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">General Settings</h3>
                    <p class="text-sm text-gray-600">Basic site configuration</p>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Site Name</label>
                        <input type="text" name="site_name" value="{{ old('site_name', $settings['site_name'] ?? 'Mbuni FC') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
                        @error('site_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Site Description</label>
                        <textarea name="site_description" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>{{ old('site_description', $settings['site_description'] ?? 'Official website of Mbuni FC - Arusha\'s premier football club') }}</textarea>
                        @error('site_description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Contact Email</label>
                        <input type="email" name="contact_email" value="{{ old('contact_email', $settings['contact_email'] ?? '<EMAIL>') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
                        @error('contact_email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Contact Phone</label>
                        <input type="tel" name="contact_phone" value="{{ old('contact_phone', $settings['contact_phone'] ?? '+255 123 456 789') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
                        @error('contact_phone')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Contact Address</label>
                        <input type="text" name="contact_address" value="{{ old('contact_address', $settings['contact_address'] ?? 'Arusha, Tanzania') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
                        @error('contact_address')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Social Media Settings -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Social Media</h3>
                    <p class="text-sm text-gray-600">Social media links and profiles</p>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Facebook</label>
                        <input type="url" name="facebook_url" value="{{ old('facebook_url', $settings['facebook_url'] ?? '') }}"
                               placeholder="https://facebook.com/mbunifc"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        @error('facebook_url')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Twitter</label>
                        <input type="url" name="twitter_url" value="{{ old('twitter_url', $settings['twitter_url'] ?? '') }}"
                               placeholder="https://twitter.com/mbunifc"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        @error('twitter_url')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Instagram</label>
                        <input type="url" name="instagram_url" value="{{ old('instagram_url', $settings['instagram_url'] ?? '') }}"
                               placeholder="https://instagram.com/mbunifc"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        @error('instagram_url')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">YouTube</label>
                        <input type="url" name="youtube_url" value="{{ old('youtube_url', $settings['youtube_url'] ?? '') }}"
                               placeholder="https://youtube.com/mbunifc"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        @error('youtube_url')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Club Information -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Club Information</h3>
                    <p class="text-sm text-gray-600">Basic club details</p>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Founded Year</label>
                        <input type="number" name="founded_year" value="{{ old('founded_year', $settings['founded_year'] ?? '2020') }}"
                               min="1900" max="{{ date('Y') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
                        @error('founded_year')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Home Stadium</label>
                        <input type="text" name="home_stadium" value="{{ old('home_stadium', $settings['home_stadium'] ?? 'Sheikh Amri Abeid Memorial Stadium') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
                        @error('home_stadium')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Stadium Capacity</label>
                        <input type="number" name="stadium_capacity" value="{{ old('stadium_capacity', $settings['stadium_capacity'] ?? '10000') }}"
                               min="1" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
                        @error('stadium_capacity')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Club Colors</label>
                        <input type="text" name="club_colors" value="{{ old('club_colors', $settings['club_colors'] ?? 'Red and White') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
                        @error('club_colors')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- SEO Settings -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">SEO Settings</h3>
                    <p class="text-sm text-gray-600">Search engine optimization</p>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Meta Keywords</label>
                        <input type="text" name="meta_keywords" value="{{ old('meta_keywords', $settings['meta_keywords'] ?? 'Mbuni FC, football, Tanzania, Arusha, soccer') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        @error('meta_keywords')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Content Management -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Homepage Content</h3>
                    <p class="text-sm text-gray-600">Manage homepage hero and about sections</p>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Hero Title</label>
                        <input type="text" name="hero_title" value="{{ old('hero_title', $settings['hero_title'] ?? 'Welcome to Mbuni FC') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        @error('hero_title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Hero Subtitle</label>
                        <textarea name="hero_subtitle" rows="2"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">{{ old('hero_subtitle', $settings['hero_subtitle'] ?? 'Arusha\'s Premier Football Club') }}</textarea>
                        @error('hero_subtitle')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Club Values -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Club Values</h3>
                    <p class="text-sm text-gray-600">Mission, vision, and values statements</p>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Mission Statement</label>
                        <textarea name="mission_statement" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">{{ old('mission_statement', $settings['mission_statement'] ?? 'To develop and promote football excellence while building strong community connections.') }}</textarea>
                        @error('mission_statement')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Vision Statement</label>
                        <textarea name="vision_statement" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">{{ old('vision_statement', $settings['vision_statement'] ?? 'To be the leading football club in Tanzania, inspiring our community through sport.') }}</textarea>
                        @error('vision_statement')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Values Statement</label>
                        <textarea name="values_statement" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">{{ old('values_statement', $settings['values_statement'] ?? 'Excellence, Integrity, Teamwork, Community, Passion') }}</textarea>
                        @error('values_statement')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Contact Map -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Contact Map</h3>
                    <p class="text-sm text-gray-600">Google Maps iframe for contact page</p>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Map Iframe Code</label>
                        <textarea name="contact_map_iframe" rows="4"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                                  placeholder='<iframe src="https://www.google.com/maps/embed?pb=..." width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy"></iframe>'>{{ old('contact_map_iframe', $settings['contact_map_iframe'] ?? '') }}</textarea>
                        @error('contact_map_iframe')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Paste the complete iframe code from Google Maps</p>
                    </div>
                </div>
            </div>

            <!-- Site Metrics -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Site Metrics</h3>
                    <p class="text-sm text-gray-600">Manage dynamic metrics displayed across the site</p>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <!-- <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Total Fans</label>
                            <input type="number" name="total_fans" value="{{ old('total_fans', $settings['total_fans'] ?? '1000') }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            @error('total_fans')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div> -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Total Events</label>
                            <input type="number" name="total_events" value="{{ old('total_events', $settings['total_events'] ?? '50') }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            @error('total_events')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Squad Metrics -->
                    <div class="mt-6">
                        <h4 class="text-md font-medium text-gray-900 mb-3">Squad Statistics</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Years Experience</label>
                                <input type="number" name="years_experience" value="{{ old('years_experience', $settings['years_experience'] ?? '15') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                                @error('years_experience')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <small class="text-gray-500 text-xs">Club's years of experience (manual setting)</small>
                            </div>
                        </div>
                        <div class="mt-3 p-3 bg-blue-50 rounded-lg">
                            <p class="text-sm text-blue-700">
                                <strong>Note:</strong> Total players, average age, and nationalities are automatically calculated from player data.
                            </p>
                        </div>
                    </div>

                    <!-- League Settings -->
                    <div class="mt-6">
                        <h4 class="text-md font-medium text-gray-900 mb-3">League Settings</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Total Matches Per Season</label>
                                <input type="number" name="matches_per_season" value="{{ old('matches_per_season', $settings['matches_per_season'] ?? '38') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                                @error('matches_per_season')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <small class="text-gray-500 text-xs">Used to calculate season progress percentage</small>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Current League</label>
                                <input type="text" name="current_league" value="{{ old('current_league', $settings['current_league'] ?? 'Premier League') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                                @error('current_league')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <small class="text-gray-500 text-xs">Name of the league/competition</small>
                            </div>
                        </div>
                        <div class="mt-3 p-3 bg-green-50 rounded-lg">
                            <p class="text-sm text-green-700">
                                <strong>Auto-calculated:</strong> Season progress is automatically calculated based on finished matches vs total matches per season.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <!-- About Page Management -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">About Page Management</h3>
                <p class="text-sm text-gray-600">Manage all content sections of the About page</p>
            </div>
            <div class="p-6 space-y-8">
                <!-- Club Overview Section -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4">Club Overview Section</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Club Overview Title</label>
                            <input type="text" name="about_overview_title" value="{{ old('about_overview_title', $settings['about_overview_title'] ?? 'Overview') }}"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            @error('about_overview_title')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Overview Image</label>
                            <input type="file" name="about_overview_image" accept="image/*"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            @if($settings['about_overview_image'] ?? null)
                                <div class="mt-2">
                                    <img src="{{ asset('storage/' . $settings['about_overview_image']) }}" alt="Current overview image" class="w-20 h-12 object-cover rounded">
                                    <p class="text-xs text-gray-500">Current image</p>
                                </div>
                            @endif
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">About Description</label>
                            <textarea name="about_description" rows="4"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">{{ old('about_description', $settings['about_description'] ?? 'We are a passionate football club dedicated to excellence, community, and the beautiful game.') }}</textarea>
                            @error('about_description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Overview Content</label>
                            <textarea name="about_overview_content" rows="4"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Write about the club's overview...">{{ old('about_overview_content', $settings['about_overview_content'] ?? '') }}</textarea>
                        </div>
                    </div>
                </div>

                <!-- Club Facts Section -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4">Club Facts</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Total Trophies</label>
                            <input type="number" name="total_trophies" value="{{ old('total_trophies', $settings['total_trophies'] ?? '15') }}"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" min="0">
                            <small class="text-gray-500 text-xs">Number of trophies won by the club</small>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Registered Fans</label>
                            <input type="number" name="registered_fans" value="{{ old('registered_fans', $settings['registered_fans'] ?? '5000') }}"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" min="0">
                            <small class="text-gray-500 text-xs">Number of registered fans</small>
                        </div>
                    </div>
                    <div class="mt-3 p-3 bg-blue-50 rounded-lg">
                        <p class="text-sm text-blue-700">
                            <strong>Auto-calculated:</strong> Founded year, years active, and squad size are automatically calculated from existing data.
                        </p>
                    </div>
                </div>

                <!-- Stadium Information -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4">Stadium Information</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Stadium Name</label>
                            <input type="text" name="stadium_name" value="{{ old('stadium_name', $settings['stadium_name'] ?? $settings['home_stadium'] ?? '') }}"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Stadium Location</label>
                            <input type="text" name="stadium_location" value="{{ old('stadium_location', $settings['stadium_location'] ?? 'Arusha, Tanzania') }}"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Stadium Standard</label>
                            <select name="stadium_standard" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                                <option value="FIFA Standard" {{ old('stadium_standard', $settings['stadium_standard'] ?? '') === 'FIFA Standard' ? 'selected' : '' }}>FIFA Standard</option>
                                <option value="International" {{ old('stadium_standard', $settings['stadium_standard'] ?? '') === 'International' ? 'selected' : '' }}>International</option>
                                <option value="Professional" {{ old('stadium_standard', $settings['stadium_standard'] ?? '') === 'Professional' ? 'selected' : '' }}>Professional</option>
                                <option value="Semi-Professional" {{ old('stadium_standard', $settings['stadium_standard'] ?? '') === 'Semi-Professional' ? 'selected' : '' }}>Semi-Professional</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Stadium Image</label>
                            <input type="file" name="stadium_image" accept="image/*"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            @if($settings['stadium_image'] ?? null)
                                <div class="mt-2">
                                    <img src="{{ asset('storage/' . $settings['stadium_image']) }}" alt="Current stadium image" class="w-20 h-12 object-cover rounded">
                                    <p class="text-xs text-gray-500">Current image</p>
                                </div>
                            @endif
                        </div>
                    </div>
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Stadium Description</label>
                        <textarea name="stadium_description" rows="3"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Describe the stadium facilities and features...">{{ old('stadium_description', $settings['stadium_description'] ?? '') }}</textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- History Page Management -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">History Page Management</h3>
                <p class="text-sm text-gray-600">Manage timeline events and achievements</p>
            </div>
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h4 class="text-md font-medium text-gray-900">Timeline Events</h4>
                    <button onclick="openTimelineManager()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                        Manage Timeline Events
                    </button>
                </div>

                <!-- Timeline Events List -->
                <div class="space-y-4">
                    @forelse($timelineEvents as $event)
                        <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-4">
                                        <div class="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-sm"
                                            style="background-color: {{ $event->color }}">
                                            {{ $event->year }}
                                        </div>
                                        <div>
                                            <h5 class="font-semibold text-gray-900">{{ $event->title }}</h5>
                                            <p class="text-sm text-gray-600">{{ Str::limit($event->description, 100) }}</p>
                                            @if($event->achievement)
                                                <p class="text-xs text-blue-600 font-medium">Achievement: {{ $event->achievement }}</p>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-xs px-2 py-1 rounded-full {{ $event->is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                        {{ $event->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                    <button onclick="editTimelineEvent({{ $event->id }})" class="text-blue-600 hover:text-blue-800">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </button>
                                    <button onclick="deleteTimelineEvent({{ $event->id }})" class="text-red-600 hover:text-red-800">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-8 text-gray-500">
                            <p>No timeline events created yet.</p>
                            <button onclick="openTimelineManager()" class="mt-2 text-blue-600 hover:text-blue-800">
                                Create your first timeline event
                            </button>
                        </div>
                    @endforelse
                </div>

                <!-- Achievement Numbers -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4">Our Achievements Section</h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Matches Played</label>
                            <input type="number" name="total_matches_played" value="{{ old('total_matches_played', $settings['total_matches_played'] ?? '500') }}"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" min="0">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Victories</label>
                            <input type="number" name="total_victories" value="{{ old('total_victories', $settings['total_victories'] ?? '320') }}"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" min="0">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Goals Scored</label>
                            <input type="number" name="total_goals_scored" value="{{ old('total_goals_scored', $settings['total_goals_scored'] ?? '1250') }}"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" min="0">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Clean Sheets</label>
                            <input type="number" name="total_clean_sheets" value="{{ old('total_clean_sheets', $settings['total_clean_sheets'] ?? '180') }}"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" min="0">
                        </div>
                    </div>

                    <!-- Achievement Awards -->
                    <div class="mt-6">
                        <h4 class="text-md font-medium text-gray-900 mb-3">Achievement Awards</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">League Titles</label>
                                <input type="number" name="league_titles" value="{{ old('league_titles', $settings['league_titles'] ?? '3') }}"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" min="0">
                                <small class="text-gray-500 text-xs">Number of league championships won</small>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Cup Victories</label>
                                <input type="number" name="cup_victories" value="{{ old('cup_victories', $settings['cup_victories'] ?? '7') }}"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" min="0">
                                <small class="text-gray-500 text-xs">Number of cup competitions won</small>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Community Awards</label>
                                <input type="number" name="community_awards" value="{{ old('community_awards', $settings['community_awards'] ?? '5') }}"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" min="0">
                                <small class="text-gray-500 text-xs">Community development and social responsibility awards</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>                                
    <!-- System Information -->
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">System Information</h3>
            <p class="text-sm text-gray-600">Current system status and information</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">{{ \App\Models\News::count() }}</div>
                    <div class="text-sm text-gray-600">Total News Articles</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">{{ \App\Models\Player::count() }}</div>
                    <div class="text-sm text-gray-600">Total Players</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-yellow-600">{{ \App\Models\Fixture::count() }}</div>
                    <div class="text-sm text-gray-600">Total Fixtures</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">{{ \App\Models\Merchandise::count() }}</div>
                    <div class="text-sm text-gray-600">Total Products</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Page Content Editor Modal -->
<div id="pageEditorModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex justify-between items-center pb-3 border-b">
                <h3 id="modalTitle" class="text-lg font-semibold text-gray-900">Edit Page Content</h3>
                <button onclick="closePageEditor()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Page Editor Form -->
            <form id="pageEditorForm" class="mt-6 space-y-6">
                @csrf
                <input type="hidden" id="pageKey" name="page_key">

                <!-- Title -->
                <div>
                    <label for="pageTitle" class="block text-sm font-medium text-gray-700 mb-2">Page Title</label>
                    <input type="text"
                           id="pageTitle"
                           name="title"
                           required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Image Upload -->
                <div>
                    <label for="pageImage" class="block text-sm font-medium text-gray-700 mb-2">Header Image (Optional)</label>
                    <input type="file"
                           id="pageImage"
                           name="image"
                           accept="image/*"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    <div id="currentImage" class="mt-2 hidden">
                        <img id="currentImagePreview" src="" alt="Current image" class="w-32 h-20 object-cover rounded">
                        <p class="text-xs text-gray-500 mt-1">Current image</p>
                    </div>
                </div>

                <!-- Content Editor -->
                <div>
                    <label for="pageContent" class="block text-sm font-medium text-gray-700 mb-2">Content</label>
                    <div class="border border-gray-300 rounded-md">
                        <!-- Toolbar -->
                        <div class="border-b border-gray-300 p-2 bg-gray-50 rounded-t-md">
                            <div class="flex flex-wrap gap-2">
                                <button type="button" onclick="formatText('bold')" class="px-2 py-1 text-sm bg-white border rounded hover:bg-gray-100">
                                    <strong>B</strong>
                                </button>
                                <button type="button" onclick="formatText('italic')" class="px-2 py-1 text-sm bg-white border rounded hover:bg-gray-100">
                                    <em>I</em>
                                </button>
                                <button type="button" onclick="formatText('underline')" class="px-2 py-1 text-sm bg-white border rounded hover:bg-gray-100">
                                    <u>U</u>
                                </button>
                                <button type="button" onclick="insertHeading()" class="px-2 py-1 text-sm bg-white border rounded hover:bg-gray-100">
                                    H2
                                </button>
                                <button type="button" onclick="insertList()" class="px-2 py-1 text-sm bg-white border rounded hover:bg-gray-100">
                                    List
                                </button>
                                <button type="button" onclick="insertLink()" class="px-2 py-1 text-sm bg-white border rounded hover:bg-gray-100">
                                    Link
                                </button>
                            </div>
                        </div>
                        <textarea id="pageContent"
                                  name="content"
                                  rows="15"
                                  required
                                  class="w-full px-3 py-2 border-0 rounded-b-md focus:ring-blue-500 focus:border-blue-500 resize-none"
                                  placeholder="Enter your page content here. You can use HTML tags for formatting."></textarea>
                    </div>
                    <p class="mt-1 text-xs text-gray-500">You can use HTML tags for advanced formatting.</p>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-3 pt-4 border-t">
                    <button type="button" onclick="closePageEditor()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                        Save Content
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Timeline Events Management Modal -->
<div id="timelineManagerModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex justify-between items-center pb-3 border-b">
                <h3 class="text-lg font-semibold text-gray-900">Manage Timeline Events</h3>
                <button onclick="closeTimelineManager()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Timeline Event Form -->
            <form id="timelineEventForm" class="mt-6 space-y-6">
                @csrf
                <input type="hidden" id="eventId" name="id">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Year -->
                    <div>
                        <label for="eventYear" class="block text-sm font-medium text-gray-700 mb-2">Year</label>
                        <input type="number" id="eventYear" name="year" min="1900" max="{{ date('Y') }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <!-- Color -->
                    <div>
                        <label for="eventColor" class="block text-sm font-medium text-gray-700 mb-2">Color</label>
                        <input type="color" id="eventColor" name="color" value="#dc2626" required
                               class="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>

                <!-- Title -->
                <div>
                    <label for="eventTitle" class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                    <input type="text" id="eventTitle" name="title" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Description -->
                <div>
                    <label for="eventDescription" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea id="eventDescription" name="description" rows="3" required
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>

                <!-- Achievement -->
                <div>
                    <label for="eventAchievement" class="block text-sm font-medium text-gray-700 mb-2">Key Achievement (Optional)</label>
                    <input type="text" id="eventAchievement" name="achievement"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Sort Order -->
                    <div>
                        <label for="eventSortOrder" class="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
                        <input type="number" id="eventSortOrder" name="sort_order" min="0" value="0"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <!-- Active Status -->
                    <div class="flex items-center">
                        <input type="checkbox" id="eventIsActive" name="is_active" checked
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="eventIsActive" class="ml-2 block text-sm text-gray-900">Active</label>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-between pt-4 border-t">
                    <button type="button" onclick="resetTimelineForm()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors">
                        New Event
                    </button>
                    <div class="space-x-3">
                        <button type="button" onclick="closeTimelineManager()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors">
                            Close
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                            Save Event
                        </button>
                    </div>
                </div>
            </form>

            <!-- Events List -->
            <div class="mt-8 border-t pt-6">
                <h4 class="text-md font-medium text-gray-900 mb-4">Current Timeline Events</h4>
                <div id="timelineEventsList" class="space-y-3 max-h-60 overflow-y-auto">
                    <!-- Events will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentPageKey = '';

function openPageEditor(pageKey) {
    currentPageKey = pageKey;
    document.getElementById('pageKey').value = pageKey;
    document.getElementById('modalTitle').textContent = `Edit ${pageKey.charAt(0).toUpperCase() + pageKey.slice(1)} Page`;

    // Load existing content
    fetch(`/admin/page-content/${pageKey}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.page) {
                document.getElementById('pageTitle').value = data.page.title || '';
                document.getElementById('pageContent').value = data.page.content || '';

                if (data.page.image) {
                    document.getElementById('currentImage').classList.remove('hidden');
                    document.getElementById('currentImagePreview').src = `/storage/${data.page.image}`;
                } else {
                    document.getElementById('currentImage').classList.add('hidden');
                }
            } else {
                // New page
                document.getElementById('pageTitle').value = pageKey.charAt(0).toUpperCase() + pageKey.slice(1);
                document.getElementById('pageContent').value = '';
                document.getElementById('currentImage').classList.add('hidden');
            }
        })
        .catch(error => {
            console.error('Error loading page content:', error);
            // Set defaults for new page
            document.getElementById('pageTitle').value = pageKey.charAt(0).toUpperCase() + pageKey.slice(1);
            document.getElementById('pageContent').value = '';
            document.getElementById('currentImage').classList.add('hidden');
        });

    document.getElementById('pageEditorModal').classList.remove('hidden');
}

function closePageEditor() {
    document.getElementById('pageEditorModal').classList.add('hidden');
    document.getElementById('pageEditorForm').reset();
}

// Form submission
document.getElementById('pageEditorForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch('/admin/page-content', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closePageEditor();
            location.reload(); // Reload to show updated content
        } else {
            alert('Error saving content: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error saving content');
    });
});

// Text formatting functions
function formatText(command) {
    const textarea = document.getElementById('pageContent');
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);

    let formattedText = '';
    switch(command) {
        case 'bold':
            formattedText = `<strong>${selectedText}</strong>`;
            break;
        case 'italic':
            formattedText = `<em>${selectedText}</em>`;
            break;
        case 'underline':
            formattedText = `<u>${selectedText}</u>`;
            break;
    }

    textarea.value = textarea.value.substring(0, start) + formattedText + textarea.value.substring(end);
    textarea.focus();
    textarea.setSelectionRange(start + formattedText.length, start + formattedText.length);
}

function insertHeading() {
    const textarea = document.getElementById('pageContent');
    const start = textarea.selectionStart;
    const selectedText = textarea.value.substring(textarea.selectionStart, textarea.selectionEnd) || 'Heading';
    const headingText = `<h2>${selectedText}</h2>`;

    textarea.value = textarea.value.substring(0, start) + headingText + textarea.value.substring(textarea.selectionEnd);
    textarea.focus();
}

function insertList() {
    const textarea = document.getElementById('pageContent');
    const start = textarea.selectionStart;
    const listText = `<ul>\n  <li>Item 1</li>\n  <li>Item 2</li>\n  <li>Item 3</li>\n</ul>`;

    textarea.value = textarea.value.substring(0, start) + listText + textarea.value.substring(textarea.selectionEnd);
    textarea.focus();
}

function insertLink() {
    const textarea = document.getElementById('pageContent');
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end) || 'Link Text';
    const linkText = `<a href="https://example.com">${selectedText}</a>`;

    textarea.value = textarea.value.substring(0, start) + linkText + textarea.value.substring(end);
    textarea.focus();
}

// Close modal when clicking outside
document.getElementById('pageEditorModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closePageEditor();
    }
});

// Timeline Events Management
function openTimelineManager() {
    document.getElementById('timelineManagerModal').classList.remove('hidden');
    loadTimelineEvents();
}

function closeTimelineManager() {
    document.getElementById('timelineManagerModal').classList.add('hidden');
    resetTimelineForm();
}

function resetTimelineForm() {
    document.getElementById('timelineEventForm').reset();
    document.getElementById('eventId').value = '';
    document.getElementById('eventIsActive').checked = true;
    document.getElementById('eventColor').value = '#dc2626';
    document.getElementById('eventSortOrder').value = '0';
}

function loadTimelineEvents() {
    fetch('/admin/timeline-events')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayTimelineEvents(data.events);
            }
        })
        .catch(error => {
            console.error('Error loading timeline events:', error);
        });
}

function displayTimelineEvents(events) {
    const container = document.getElementById('timelineEventsList');

    if (events.length === 0) {
        container.innerHTML = '<p class="text-gray-500 text-center py-4">No timeline events found.</p>';
        return;
    }

    container.innerHTML = events.map(event => `
        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-bold"
                     style="background-color: ${event.color}">
                    ${event.year}
                </div>
                <div>
                    <h5 class="font-medium text-gray-900">${event.title}</h5>
                    <p class="text-xs text-gray-600">${event.description.substring(0, 50)}${event.description.length > 50 ? '...' : ''}</p>
                </div>
            </div>
            <div class="flex items-center space-x-2">
                <span class="text-xs px-2 py-1 rounded-full ${event.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                    ${event.is_active ? 'Active' : 'Inactive'}
                </span>
                <button onclick="editTimelineEvent(${event.id})" class="text-blue-600 hover:text-blue-800">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                </button>
                <button onclick="deleteTimelineEvent(${event.id})" class="text-red-600 hover:text-red-800">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            </div>
        </div>
    `).join('');
}

function editTimelineEvent(eventId) {
    // Find the event data from the loaded events
    fetch('/admin/timeline-events')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const event = data.events.find(e => e.id === eventId);
                if (event) {
                    document.getElementById('eventId').value = event.id;
                    document.getElementById('eventYear').value = event.year;
                    document.getElementById('eventTitle').value = event.title;
                    document.getElementById('eventDescription').value = event.description;
                    document.getElementById('eventAchievement').value = event.achievement || '';
                    document.getElementById('eventColor').value = event.color;
                    document.getElementById('eventSortOrder').value = event.sort_order || 0;
                    document.getElementById('eventIsActive').checked = event.is_active;
                }
            }
        });
}

function deleteTimelineEvent(eventId) {
    if (!confirm('Are you sure you want to delete this timeline event?')) {
        return;
    }

    fetch('/admin/timeline-events', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ id: eventId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadTimelineEvents();
            showMessage(data.message, 'success');
        } else {
            showMessage('Error deleting timeline event', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Error deleting timeline event', 'error');
    });
}

// Timeline form submission
document.getElementById('timelineEventForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());

    // Convert checkbox to boolean
    data.is_active = document.getElementById('eventIsActive').checked;

    fetch('/admin/timeline-events', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resetTimelineForm();
            loadTimelineEvents();
            showMessage(data.message, 'success');
        } else {
            showMessage('Error saving timeline event', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Error saving timeline event', 'error');
    });
});

// Close timeline modal when clicking outside
document.getElementById('timelineManagerModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeTimelineManager();
    }
});

// Helper function to show messages
function showMessage(message, type) {
    // Create a temporary message element
    const messageDiv = document.createElement('div');
    messageDiv.className = `fixed top-4 right-4 z-50 p-4 rounded-md ${type === 'success' ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'}`;
    messageDiv.textContent = message;

    document.body.appendChild(messageDiv);

    // Remove after 3 seconds
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}
</script>
@endsection
