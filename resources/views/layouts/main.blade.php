<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', $siteSettings['site_name'] ?? 'Mbuni FC') - Official Website</title>
    <meta name="description" content="@yield('description', $siteSettings['site_description'] ?? 'Official website of Mbuni FC - Arusha\'s premier football club. Get the latest news, fixtures, squad information, and merchandise.')">
    @if($siteSettings['meta_keywords'] ?? false)
    <meta name="keywords" content="{{ $siteSettings['meta_keywords'] }}">
    @endif

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Additional head content -->
    @stack('head')
</head>
<body class="font-sans antialiased">
    <!-- Loading Overlay -->
    <!-- <div class="loading-overlay" id="loading-overlay">
        <div class="text-center">
            <div class="loading-spinner mx-auto mb-4"></div>
            <div class="text-gray-600 font-medium">Loading Mbuni FC...</div>
        </div>
    </div> -->

    <!-- Modern Navigation -->
    <nav class="glass-dark backdrop-blur-md bg-white/98 shadow-xl sticky top-0 z-50 transition-all duration-300" id="main-nav">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-20">
                <div class="flex items-center">
                    <!-- Enhanced Logo -->
                    <a href="{{ route('home') }}" class="flex items-center group">
                        <div class="relative">
                            <img src="{{ asset('images/mbuni-logo.svg') }}" alt="Mbuni FC" class="h-12 w-12 mr-3 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12">
                            <div class="absolute inset-0 bg-primary-500/20 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>
                        <div>
                            <span class="text-2xl font-black text-primary-600 group-hover:text-primary-700 transition-colors duration-300">{{ $siteSettings['site_name'] ?? 'Mbuni FC' }}</span>
                            <div class="text-xs text-gray-500 font-medium">{{ $siteSettings['club_colors'] ?? 'Arusha\'s Pride' }}</div>
                        </div>
                    </a>
                </div>

                <!-- Desktop Navigation with Modern Effects -->
                <div class="hidden md:flex items-center space-x-2">
                    <a href="{{ route('home') }}" class="nav-link-modern {{ request()->routeIs('home') ? 'active' : '' }}">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                            </svg>
                            Home
                        </span>
                    </a>
                    <a href="{{ route('news.index') }}" class="nav-link-modern {{ request()->routeIs('news.*') ? 'active' : '' }}">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                            </svg>
                            News
                        </span>
                    </a>
                    <a href="{{ route('fixtures.index') }}" class="nav-link-modern {{ request()->routeIs('fixtures.*') ? 'active' : '' }}">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            Fixtures
                        </span>
                    </a>
                    <a href="{{ route('squad.index') }}" class="nav-link-modern {{ request()->routeIs('squad.*') || request()->routeIs('players.*') ? 'active' : '' }}">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            Squad
                        </span>
                    </a>

                    <!-- Enhanced Club Dropdown -->
                    <div class="relative group">
                        <button class="nav-link-modern flex items-center {{ request()->routeIs('club.*') ? 'active' : '' }}">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            Club
                            <svg class="ml-1 h-4 w-4 transition-transform duration-300 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="absolute left-0 mt-2 w-56 glass-card rounded-2xl shadow-2xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                            <div class="p-2">
                                <a href="{{ route('club.index') }}" class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-600 rounded-xl transition-all duration-200">
                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    About Club
                                </a>
                                <a href="{{ route('club.history') }}" class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-600 rounded-xl transition-all duration-200">
                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    History
                                </a>
                            </div>
                        </div>
                    </div>

                    <a href="{{ route('tickets.index') }}" class="nav-link-modern {{ request()->routeIs('tickets.*') ? 'active' : '' }}">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5h2.5a2.5 2.5 0 0 1 0 5H5m0 0h2.5a2.5 2.5 0 0 1 0 5H5"></path>
                            </svg>
                            Tickets
                        </span>
                    </a>
                    <a href="{{ route('store.index') }}" class="nav-link-modern {{ request()->routeIs('store.*') ? 'active' : '' }}">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                            </svg>
                            Store
                        </span>
                    </a>
                    <a href="{{ route('media.index') }}" class="nav-link-modern {{ request()->routeIs('media.*') ? 'active' : '' }}">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            Media
                        </span>
                    </a>
                    <a href="{{ route('contact.index') }}" class="nav-link-modern {{ request()->routeIs('contact.*') ? 'active' : '' }}">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            Contact
                        </span>
                    </a>
                </div>

                <!-- Enhanced Right Side Actions -->
                <div class="hidden md:flex items-center space-x-3">
                    @auth
                        <!-- Enhanced Cart Icon with Neumorphism -->
                        <!-- <a href="{{ route('cart.index') }}" class="neomorphism p-3 relative text-gray-700 hover:text-primary-600 transition-all duration-300 hover:scale-105 cart-icon">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6"></path>
                            </svg>
                            @if(session('cart') && count(session('cart')) > 0)
                                <span class="absolute -top-1 -right-1 bg-gradient-to-r from-primary-500 to-primary-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold animate-pulse">
                                    {{ array_sum(array_column(session('cart'), 'quantity')) }}
                                </span>
                            @endif
                        </a> -->

                        <!-- Quick Actions -->
                        <!-- <a href="{{ route('tickets.index') }}" class="neomorphism p-3 text-gray-700 hover:text-yellow-600 transition-all duration-300 hover:scale-105" title="Quick Tickets">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5h2.5a2.5 2.5 0 0 1 0 5H5m0 0h2.5a2.5 2.5 0 0 1 0 5H5"></path>
                            </svg>
                        </a> -->

                        <!-- Enhanced User Dropdown -->
                        <div class="relative group">
                            <button class="neomorphism p-3 flex items-center text-gray-700 hover:text-primary-600 transition-all duration-300 hover:scale-105">
                                <div class="w-6 h-6 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white text-sm font-bold mr-2">
                                    {{ substr(Auth::user()->name, 0, 1) }}
                                </div>
                                <span class="font-medium">{{ Str::limit(Auth::user()->name, 10) }}</span>
                                <svg class="h-4 w-4 ml-1 transition-transform duration-300 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="absolute right-0 mt-2 w-56 glass-card rounded-2xl shadow-2xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                                <div class="p-2">
                                    <div class="px-4 py-3 border-b border-gray-200/50">
                                        <p class="text-sm font-semibold text-gray-900">{{ Auth::user()->name }}</p>
                                        <p class="text-xs text-gray-500">{{ Auth::user()->email }}</p>
                                    </div>
                                    <a href="{{ route('dashboard') }}" class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-600 rounded-xl transition-all duration-200">
                                        <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2zm9-3h4m-4 2h4m-4-8V5a2 2 0 011-1.732l1-1.732"></path>
                                        </svg>
                                        Dashboard
                                    </a>
                                    <a href="{{ route('profile.edit') }}" class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-600 rounded-xl transition-all duration-200">
                                        <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                        Profile
                                    </a>
                                    @if(Auth::user()->canManageContent())
                                        <a href="{{ route('admin.dashboard') }}" class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-600 rounded-xl transition-all duration-200">
                                            <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                            Admin Panel
                                        </a>
                                    @endif
                                    <div class="border-t border-gray-200/50 mt-2 pt-2">
                                        <form method="POST" action="{{ route('logout') }}">
                                            @csrf
                                            <button type="submit" class="flex items-center w-full px-4 py-3 text-sm text-red-600 hover:bg-red-50 rounded-xl transition-all duration-200">
                                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                                </svg>
                                                Logout
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endauth
                </div>

                <!-- Enhanced Mobile Menu Button -->
                <div class="md:hidden flex items-center">
                    <button type="button" class="mobile-menu-button neomorphism p-3 text-gray-700 hover:text-primary-600 focus:outline-none transition-all duration-300 hover:scale-105" id="mobile-menu-toggle">
                        <svg class="h-6 w-6 transition-transform duration-300" id="menu-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                        <svg class="h-6 w-6 hidden transition-transform duration-300" id="close-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Enhanced Mobile Navigation Drawer -->
        <div class="mobile-menu fixed inset-y-0 right-0 w-80 bg-white shadow-2xl transform translate-x-full transition-transform duration-300 ease-in-out z-50 md:hidden" id="mobile-drawer">
            <div class="h-full flex flex-col">
                <!-- Mobile Header -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200">
                    <div class="flex items-center">
                        <img src="{{ asset('images/mbuni-logo.svg') }}" alt="Mbuni FC" class="h-8 w-8 mr-2">
                        <span class="text-lg font-bold text-primary-600">Mbuni FC</span>
                    </div>
                    <button class="mobile-menu-close p-2 text-gray-500 hover:text-gray-700">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Mobile Navigation Links -->
                <div class="flex-1 overflow-y-auto py-6">
                    <nav class="px-6 space-y-2">
                        <a href="{{ route('home') }}" class="flex items-center px-4 py-3 text-gray-700 hover:bg-primary-50 hover:text-primary-600 rounded-xl transition-all duration-200 {{ request()->routeIs('home') ? 'bg-primary-50 text-primary-600' : '' }}">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                            </svg>
                            Home
                        </a>
                        <a href="{{ route('news.index') }}" class="flex items-center px-4 py-3 text-gray-700 hover:bg-primary-50 hover:text-primary-600 rounded-xl transition-all duration-200 {{ request()->routeIs('news.*') ? 'bg-primary-50 text-primary-600' : '' }}">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                            </svg>
                            News
                        </a>
                        <a href="{{ route('fixtures.index') }}" class="flex items-center px-4 py-3 text-gray-700 hover:bg-primary-50 hover:text-primary-600 rounded-xl transition-all duration-200 {{ request()->routeIs('fixtures.*') ? 'bg-primary-50 text-primary-600' : '' }}">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            Fixtures
                        </a>
                        <a href="{{ route('squad.index') }}" class="flex items-center px-4 py-3 text-gray-700 hover:bg-primary-50 hover:text-primary-600 rounded-xl transition-all duration-200 {{ request()->routeIs('squad.*') ? 'bg-primary-50 text-primary-600' : '' }}">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            Squad
                        </a>
                        <a href="{{ route('club.index') }}" class="flex items-center px-4 py-3 text-gray-700 hover:bg-primary-50 hover:text-primary-600 rounded-xl transition-all duration-200 {{ request()->routeIs('club.*') ? 'bg-primary-50 text-primary-600' : '' }}">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            Club
                        </a>
                        <a href="{{ route('tickets.index') }}" class="flex items-center px-4 py-3 text-gray-700 hover:bg-primary-50 hover:text-primary-600 rounded-xl transition-all duration-200 {{ request()->routeIs('tickets.*') ? 'bg-primary-50 text-primary-600' : '' }}">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5h2.5a2.5 2.5 0 0 1 0 5H5m0 0h2.5a2.5 2.5 0 0 1 0 5H5"></path>
                            </svg>
                            Tickets
                        </a>
                        <a href="{{ route('store.index') }}" class="flex items-center px-4 py-3 text-gray-700 hover:bg-primary-50 hover:text-primary-600 rounded-xl transition-all duration-200 {{ request()->routeIs('store.*') ? 'bg-primary-50 text-primary-600' : '' }}">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                            </svg>
                            Store
                        </a>
                        <a href="{{ route('media.index') }}" class="flex items-center px-4 py-3 text-gray-700 hover:bg-primary-50 hover:text-primary-600 rounded-xl transition-all duration-200 {{ request()->routeIs('media.*') ? 'bg-primary-50 text-primary-600' : '' }}">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            Media
                        </a>
                        <a href="{{ route('contact.index') }}" class="flex items-center px-4 py-3 text-gray-700 hover:bg-primary-50 hover:text-primary-600 rounded-xl transition-all duration-200 {{ request()->routeIs('contact.*') ? 'bg-primary-50 text-primary-600' : '' }}">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            Contact
                        </a>
                    </nav>

                    <!-- Mobile User Section -->
                    @auth
                        <div class="px-6 mt-8 pt-6 border-t border-gray-200">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white font-bold mr-3">
                                    {{ substr(Auth::user()->name, 0, 1) }}
                                </div>
                                <div>
                                    <p class="font-semibold text-gray-900">{{ Auth::user()->name }}</p>
                                    <p class="text-sm text-gray-500">{{ Auth::user()->email }}</p>
                                </div>
                            </div>
                            <div class="space-y-2">
                                <a href="{{ route('dashboard') }}" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-50 rounded-lg">
                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2zm9-3h4m-4 2h4m-4-8V5a2 2 0 011-1.732l1-1.732"></path>
                                    </svg>
                                    Dashboard
                                </a>
                                <a href="{{ route('cart.index') }}" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-50 rounded-lg">
                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6"></path>
                                    </svg>
                                    Cart
                                    @if(session('cart') && count(session('cart')) > 0)
                                        <span class="ml-auto bg-primary-600 text-white text-xs rounded-full px-2 py-1">
                                            {{ array_sum(array_column(session('cart'), 'quantity')) }}
                                        </span>
                                    @endif
                                </a>
                                @if(Auth::user()->canManageContent())
                                    <a href="{{ route('admin.dashboard') }}" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-50 rounded-lg">
                                        <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        Admin Panel
                                    </a>
                                @endif
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="flex items-center w-full px-4 py-2 text-red-600 hover:bg-red-50 rounded-lg">
                                        <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                        </svg>
                                        Logout
                                    </button>
                                </form>
                            </div>
                        </div>
                    @endauth
                </div>
            </div>
        </div>

        <!-- Mobile Menu Overlay -->
        <div class="mobile-menu-overlay fixed inset-0 bg-black bg-opacity-50 z-40 hidden md:hidden" id="mobile-overlay"></div>
    </nav>

    <!-- Flash Messages -->
    @if(session('success'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mx-4 mt-4" role="alert">
            <span class="block sm:inline">{{ session('success') }}</span>
        </div>
    @endif

    @if(session('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mx-4 mt-4" role="alert">
            <span class="block sm:inline">{{ session('error') }}</span>
        </div>
    @endif

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Club Info -->
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center mb-4">
                        <img src="{{ asset('images/mbuni-logo.svg') }}" alt="Mbuni FC" class="h-12 w-12 mr-3">
                        <span class="text-2xl font-bold">Mbuni FC</span>
                    </div>
                    <p class="text-gray-300 mb-4">
                        Arusha's premier football club, established with a passion for excellence and community spirit. 
                        We strive to bring pride to our city through beautiful football and strong values.
                    </p>
                    <div class="flex space-x-4">
                        @if($siteSettings['twitter_url'] ?? false)
                        <a href="{{ $siteSettings['twitter_url'] }}" target="_blank" class="text-gray-300 hover:text-white">
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        @endif
                        @if($siteSettings['facebook_url'] ?? false)
                        <a href="{{ $siteSettings['facebook_url'] }}" target="_blank" class="text-gray-300 hover:text-white">
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                            </svg>
                        </a>
                        @endif
                        @if($siteSettings['youtube_url'] ?? false)
                        <a href="{{ $siteSettings['youtube_url'] }}" target="_blank" class="text-gray-300 hover:text-white">
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                            </svg>
                        </a>
                        @endif
                        @if($siteSettings['instagram_url'] ?? false)
                        <a href="{{ $siteSettings['instagram_url'] }}" target="_blank" class="text-gray-300 hover:text-white">
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                            </svg>
                        </a>
                        @endif
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="{{ route('news.index') }}" class="text-gray-300 hover:text-white">Latest News</a></li>
                        <li><a href="{{ route('fixtures.index') }}" class="text-gray-300 hover:text-white">Fixtures & Results</a></li>
                        <li><a href="{{ route('squad.index') }}" class="text-gray-300 hover:text-white">Squad</a></li>
                        <li><a href="{{ route('store.index') }}" class="text-gray-300 hover:text-white">Official Store</a></li>
                        <li><a href="{{ route('tickets.index') }}" class="text-gray-300 hover:text-white">Tickets</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li class="flex items-center">
                            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            {{ $siteSettings['contact_address'] ?? 'Arusha, Tanzania' }}
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            {{ $siteSettings['contact_email'] ?? '<EMAIL>' }}
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                            {{ $siteSettings['contact_phone'] ?? '+255 123 456 789' }}
                        </li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-300">
                <p>&copy; {{ date('Y') }} {{ $siteSettings['site_name'] ?? 'Mbuni FC' }}. All rights reserved.</p>
                <p class="mt-2 text-sm">
                    Developed by
                    <a href="https://kadilana-mbogo.vercel.app" target="_blank" rel="noopener noreferrer"
                       class="text-primary-400 hover:text-primary-300 font-medium transition-colors duration-200">
                        Kadilana Mbogo
                    </a>
                </p>
            </div>
        </div>
    </footer>

    <!-- Enhanced Mobile Menu Script -->
    <script>
        // document.addEventListener('DOMContentLoaded', function() {
        //     const mobileMenuButton = document.querySelector('.mobile-menu-button');
        //     const mobileDrawer = document.getElementById('mobile-drawer');
        //     const mobileOverlay = document.getElementById('mobile-overlay');
        //     const mobileMenuClose = document.querySelector('.mobile-menu-close');
        //     const menuIcon = document.getElementById('menu-icon');
        //     const closeIcon = document.getElementById('close-icon');

        //     function openMobileMenu() {
        //         mobileDrawer.classList.remove('translate-x-full');
        //         mobileOverlay.classList.remove('hidden');
        //         menuIcon.classList.add('hidden');
        //         closeIcon.classList.remove('hidden');
        //         document.body.style.overflow = 'hidden';
        //     }

        //     function closeMobileMenu() {
        //         mobileDrawer.classList.add('translate-x-full');
        //         mobileOverlay.classList.add('hidden');
        //         menuIcon.classList.remove('hidden');
        //         closeIcon.classList.add('hidden');
        //         document.body.style.overflow = 'auto';
        //     }

        //     mobileMenuButton.addEventListener('click', function() {
        //         if (mobileDrawer.classList.contains('translate-x-full')) {
        //             openMobileMenu();
        //         } else {
        //             closeMobileMenu();
        //         }
        //     });

        //     mobileMenuClose.addEventListener('click', closeMobileMenu);
        //     mobileOverlay.addEventListener('click', closeMobileMenu);

        //     // Close menu when clicking on navigation links
        //     const mobileNavLinks = mobileDrawer.querySelectorAll('a');
        //     mobileNavLinks.forEach(link => {
        //         link.addEventListener('click', closeMobileMenu);
        //     });

        //     // Enhanced navbar scroll effect with color detection
        //     const navbar = document.getElementById('main-nav');
        //     const navLinks = navbar.querySelectorAll('.nav-link-modern');
        //     const logo = navbar.querySelector('a[href*="home"] span');
        //     let lastScrollY = window.scrollY;

        //     function updateNavbarStyle() {
        //         const currentScrollY = window.scrollY;
        //         const navbarRect = navbar.getBoundingClientRect();

        //         // Check what's behind the navbar
        //         const elementsBelow = document.elementsFromPoint(
        //             navbarRect.left + navbarRect.width / 2,
        //             navbarRect.bottom + 1
        //         );

        //         let isOverDarkSection = false;

        //         // Check if navbar is over a red/dark section
        //         elementsBelow.forEach(element => {
        //             const computedStyle = window.getComputedStyle(element);
        //             const bgColor = computedStyle.backgroundColor;
        //             const bgImage = computedStyle.backgroundImage;

        //             // Check for red backgrounds or gradients
        //             if (bgColor.includes('rgb(220, 38, 38)') || // primary-600
        //                 bgColor.includes('rgb(153, 27, 27)') || // primary-700
        //                 bgColor.includes('rgb(127, 29, 29)') || // primary-800
        //                 bgImage.includes('gradient') ||
        //                 element.classList.contains('bg-primary-600') ||
        //                 element.classList.contains('bg-primary-700') ||
        //                 element.classList.contains('bg-primary-800') ||
        //                 element.classList.contains('hero-gradient') ||
        //                 element.classList.contains('hero-gradient-animated')) {
        //                 isOverDarkSection = true;
        //             }
        //         });

        //         // Update navbar background and text colors
        //         if (currentScrollY > 100) {
        //             navbar.classList.add('bg-white/99', 'shadow-2xl');
        //             navbar.classList.remove('bg-white/98');
        //         } else {
        //             navbar.classList.add('bg-white/98');
        //             navbar.classList.remove('bg-white/99', 'shadow-2xl');
        //         }

        //         // Update text colors based on background
        //         if (isOverDarkSection) {
        //             navbar.classList.add('nav-over-dark');
        //             navLinks.forEach(link => {
        //                 link.classList.add('text-white', 'hover:text-yellow-300');
        //                 link.classList.remove('text-gray-700', 'hover:text-primary-600');
        //             });
        //             if (logo) {
        //                 logo.classList.add('text-white');
        //                 logo.classList.remove('text-primary-600');
        //             }
        //         } else {
        //             navbar.classList.remove('nav-over-dark');
        //             navLinks.forEach(link => {
        //                 link.classList.add('text-gray-700', 'hover:text-primary-600');
        //                 link.classList.remove('text-white', 'hover:text-yellow-300');
        //             });
        //             if (logo) {
        //                 logo.classList.add('text-primary-600');
        //                 logo.classList.remove('text-white');
        //             }
        //         }

        //         lastScrollY = currentScrollY;
        //     }

        //     window.addEventListener('scroll', updateNavbarStyle);
        //     // Initial call
        //     updateNavbarStyle();
        // });
        
        // Enhanced Navigation JavaScript with Dynamic Background Detection
        document.addEventListener('DOMContentLoaded', function() {
            const navbar = document.getElementById('main-nav');
            const navLinks = navbar.querySelectorAll('.nav-link-modern');
            const logoText = navbar.querySelector('.text-2xl');
            const logoSubtext = navbar.querySelector('.text-xs');
            const userButtons = navbar.querySelectorAll('.neomorphism');
            const mobileMenuButton = navbar.querySelector('.mobile-menu-button');
            
            let lastScrollY = window.scrollY;
            let ticking = false;

            function updateNavbarStyle() {
                const currentScrollY = window.scrollY;
                const navbarRect = navbar.getBoundingClientRect();
                
                // Get elements behind the navbar center point
                const centerX = navbarRect.left + navbarRect.width / 2;
                const belowNavY = navbarRect.bottom + 5;
                
                // Temporarily hide navbar to check what's behind it
                navbar.style.pointerEvents = 'none';
                const elementsBelow = document.elementsFromPoint(centerX, belowNavY);
                navbar.style.pointerEvents = 'auto';
                
                let backgroundType = 'red'; // default
                
                // Analyze elements behind navbar
                for (const element of elementsBelow) {
                    const computedStyle = window.getComputedStyle(element);
                    const bgColor = computedStyle.backgroundColor;
                    const bgImage = computedStyle.backgroundImage;
                    const classes = element.className;
                    
                    // Check for white/light backgrounds
                    if (
                        bgColor.includes('rgb(255, 255, 255)') ||
                        bgColor.includes('rgb(248, 250, 252)') || // slate-50
                        bgColor.includes('rgb(241, 245, 249)') || // slate-100
                        bgColor.includes('rgb(226, 232, 240)') || // slate-200
                        classes.includes('bg-white') ||
                        classes.includes('bg-gray-50') ||
                        classes.includes('bg-gray-100') ||
                        classes.includes('bg-slate-50') ||
                        classes.includes('bg-slate-100') ||
                        element.tagName === 'BODY'
                    ) {
                        backgroundType = 'white';
                        break;
                    }
                    
                    // Check for red/dark backgrounds
                    if (
                        bgColor.includes('rgb(220, 38, 38)') || // red-600
                        bgColor.includes('rgb(153, 27, 27)') || // red-700
                        bgColor.includes('rgb(127, 29, 29)') || // red-800
                        bgImage.includes('linear-gradient') ||
                        classes.includes('bg-red-') ||
                        classes.includes('bg-primary-') ||
                        classes.includes('hero-gradient') ||
                        classes.includes('bg-gray-900') ||
                        classes.includes('bg-black')
                    ) {
                        backgroundType = 'red';
                        break;
                    }
                }
                
                // Remove existing state classes
                navbar.classList.remove('nav-over-white', 'nav-over-red', 'scrolled');
                
                // Add scroll effect
                if (currentScrollY > 50) {
                    navbar.classList.add('scrolled');
                }
                
                // Apply appropriate styling based on background
                if (backgroundType === 'white') {
                    applyWhiteBackgroundStyle();
                } else {
                    applyRedBackgroundStyle();
                }
                
                lastScrollY = currentScrollY;
                ticking = false;
            }
            
            function applyWhiteBackgroundStyle() {
                navbar.classList.add('nav-over-white');
                
                // Update navbar background
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                navbar.style.borderBottom = '1px solid rgba(0, 0, 0, 0.05)';
                navbar.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.1)';
                
                // Update text colors
                navLinks.forEach(link => {
                    link.style.color = '#374151'; // gray-700
                });
                
                if (logoText) {
                    logoText.style.color = '#dc2626'; // red-600
                    logoText.style.textShadow = 'none';
                }
                
                if (logoSubtext) {
                    logoSubtext.style.color = '#6b7280'; // gray-500
                }
                
                // Update buttons
                userButtons.forEach(button => {
                    button.style.background = 'rgba(0, 0, 0, 0.05)';
                    button.style.border = '1px solid rgba(0, 0, 0, 0.1)';
                    button.style.color = '#374151';
                });
                
                if (mobileMenuButton) {
                    mobileMenuButton.style.background = 'rgba(0, 0, 0, 0.05)';
                    mobileMenuButton.style.border = '1px solid rgba(0, 0, 0, 0.1)';
                    mobileMenuButton.style.color = '#374151';
                }
            }
            
            function applyRedBackgroundStyle() {
                navbar.classList.add('nav-over-red');
                
                // Update navbar background
                navbar.style.background = 'linear-gradient(135deg, #dc2626 0%, #991b1b 50%, #7f1d1d 100%)';
                navbar.style.borderBottom = '1px solid rgba(255, 255, 255, 0.1)';
                navbar.style.boxShadow = '0 4px 20px rgba(220, 38, 38, 0.3)';
                
                // Update text colors
                navLinks.forEach(link => {
                    link.style.color = 'white';
                });
                
                if (logoText) {
                    logoText.style.color = 'white';
                    logoText.style.textShadow = '0 2px 4px rgba(0, 0, 0, 0.3)';
                }
                
                if (logoSubtext) {
                    logoSubtext.style.color = 'rgba(255, 255, 255, 0.8)';
                }
                
                // Update buttons
                userButtons.forEach(button => {
                    button.style.background = 'rgba(255, 255, 255, 0.15)';
                    button.style.border = '1px solid rgba(255, 255, 255, 0.2)';
                    button.style.color = 'white';
                });
                
                if (mobileMenuButton) {
                    mobileMenuButton.style.background = 'rgba(255, 255, 255, 0.15)';
                    mobileMenuButton.style.border = '1px solid rgba(255, 255, 255, 0.2)';
                    mobileMenuButton.style.color = 'white';
                }
            }
            
            function requestTick() {
                if (!ticking) {
                    requestAnimationFrame(updateNavbarStyle);
                    ticking = true;
                }
            }
            
            // Throttled scroll listener
            window.addEventListener('scroll', requestTick, { passive: true });
            
            // Initial call to set proper styling
            updateNavbarStyle();
            
            // Enhanced mobile menu functionality
            const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
            const mobileDrawer = document.getElementById('mobile-drawer');
            const mobileOverlay = document.getElementById('mobile-overlay');
            const mobileMenuClose = document.querySelector('.mobile-menu-close');
            const menuIcon = document.getElementById('menu-icon');
            const closeIcon = document.getElementById('close-icon');

            function openMobileMenu() {
                mobileDrawer.classList.remove('translate-x-full');
                mobileOverlay.classList.remove('hidden');
                menuIcon.classList.add('hidden');
                closeIcon.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
                
                // Add animation class
                mobileDrawer.classList.add('animate-slide-in-right');
                setTimeout(() => {
                    mobileDrawer.classList.remove('animate-slide-in-right');
                }, 600);
            }

            function closeMobileMenu() {
                mobileDrawer.classList.add('translate-x-full');
                mobileOverlay.classList.add('hidden');
                menuIcon.classList.remove('hidden');
                closeIcon.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }

            if (mobileMenuToggle) {
                mobileMenuToggle.addEventListener('click', function() {
                    if (mobileDrawer.classList.contains('translate-x-full')) {
                        openMobileMenu();
                    } else {
                        closeMobileMenu();
                    }
                });
            }

            if (mobileMenuClose) {
                mobileMenuClose.addEventListener('click', closeMobileMenu);
            }

            if (mobileOverlay) {
                mobileOverlay.addEventListener('click', closeMobileMenu);
            }

            // Close menu when clicking on navigation links
            const mobileNavLinks = mobileDrawer?.querySelectorAll('a') || [];
            mobileNavLinks.forEach(link => {
                link.addEventListener('click', closeMobileMenu);
            });

            // Enhanced intersection observer for section detection
            const sections = document.querySelectorAll('section, .hero-section, .content-section');
            
            const sectionObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target;
                        const computedStyle = window.getComputedStyle(element);
                        const bgColor = computedStyle.backgroundColor;
                        const classes = element.className;
                        
                        // Determine if this section should trigger a nav color change
                        if (classes.includes('bg-white') || 
                            classes.includes('bg-gray-50') || 
                            classes.includes('bg-slate-50') ||
                            bgColor.includes('rgb(255, 255, 255)')) {
                            // This will be handled by the main scroll function
                            requestTick();
                        } else if (classes.includes('bg-red-') || 
                                classes.includes('bg-primary-') || 
                                classes.includes('hero-gradient') ||
                                classes.includes('bg-gray-900')) {
                            requestTick();
                        }
                    }
                });
            }, {
                rootMargin: '-80px 0px -80px 0px', // Account for navbar height
                threshold: 0.1
            });

            sections.forEach(section => {
                sectionObserver.observe(section);
            });

            // Handle dropdown hovers with improved styling
            const dropdowns = navbar.querySelectorAll('.group');
            dropdowns.forEach(dropdown => {
                const dropdownMenu = dropdown.querySelector('.glass-card');
                
                dropdown.addEventListener('mouseenter', () => {
                    if (dropdownMenu) {
                        dropdownMenu.style.transform = 'translateY(0) scale(1)';
                        dropdownMenu.style.opacity = '1';
                        dropdownMenu.style.visibility = 'visible';
                    }
                });
                
                dropdown.addEventListener('mouseleave', () => {
                    if (dropdownMenu) {
                        dropdownMenu.style.transform = 'translateY(8px) scale(0.95)';
                        dropdownMenu.style.opacity = '0';
                        dropdownMenu.style.visibility = 'hidden';
                    }
                });
            });
            
            // Add ripple effect to navigation links
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    const ripple = document.createElement('div');
                    ripple.classList.add('ripple-effect');
                    
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = (e.clientX - rect.left - size / 2) + 'px';
                    ripple.style.top = (e.clientY - rect.top - size / 2) + 'px';
                    
                    this.appendChild(ripple);
                    
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
            
            // Enhanced logo click effect
            const logoLink = navbar.querySelector('a[href*="home"]');
            if (logoLink) {
                logoLink.addEventListener('click', function() {
                    const logo = this.querySelector('img');
                    logo.style.animation = 'logoSpin 1s ease-out';
                    
                    setTimeout(() => {
                        logo.style.animation = '';
                    }, 1000);
                });
            }
        });
    </script>

    @stack('scripts')
</body>
</html>
