@extends('layouts.main')

@section('title', 'Fixtures & Results')
@section('description', 'View Mbuni FC fixtures, results and match schedule. Stay updated with upcoming matches and recent results.')

@section('content')
    <!-- Enhanced <PERSON> Header -->
    <section class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white pt-24 pb-16 -mt-20 py-20 overflow-hidden">
        <!-- Background Effects -->
        <div class="absolute inset-0">
            <!-- Animated Football Icons -->
            <div class="absolute top-10 left-10 text-white/50 text-4xl animate-bounce">⚽</div>
            <div class="absolute top-20 right-20 text-white/50 text-3xl animate-bounce" style="animation-delay: 1s;">🏆</div>
            <div class="absolute bottom-20 left-20 text-white/50 text-3xl animate-bounce" style="animation-delay: 2s;">🥅</div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center fade-in-up">
                <h1 class="text-5xl md:text-6xl font-black mb-6">
                    Fixtures & <span class="text-gradient bg-gradient-to-r from-yellow-300 to-yellow-500 bg-clip-text text-transparent">Results</span>
                </h1>
                <p class="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed mb-8">
                    Follow our journey through every match, every goal, and every victory on the pitch
                </p>

                <!-- Season Progress -->
                <div class="max-w-md mx-auto glass-card p-4 rounded-2xl">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm text-white/80">Season Progress</span>
                        <span class="text-sm text-yellow-300 font-semibold">2024/25</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {{ $siteSettings['season_progress'] ?? '65' }}%"></div>
                    </div>
                    <div class="text-xs text-white/70 mt-1">{{ $siteSettings['season_progress'] ?? '65' }}% Complete</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Quick Stats -->
    <section class="py-16 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
        <!-- Background Decorations -->
        <div class="absolute top-0 right-0 w-64 h-64 bg-primary-100 rounded-full blur-3xl opacity-30 translate-x-32 -translate-y-32"></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 fade-in-up">
                <div class="neomorphism p-8 text-center hover:scale-105 transition-transform duration-300">
                    <div class="text-4xl mb-4">⚽</div>
                    <div class="text-3xl font-bold text-primary-600 animate-score" data-score="{{ $upcomingFixtures->count() }}">0</div>
                    <div class="text-gray-600 font-medium">Upcoming Matches</div>
                </div>
                <div class="neomorphism p-8 text-center hover:scale-105 transition-transform duration-300">
                    <div class="text-4xl mb-4">🏆</div>
                    <div class="text-3xl font-bold text-green-600 animate-score" data-score="{{ $siteSettings['total_wins'] ?? $recentResults->where('score_mbuni', '>', 'score_opponent')->count() }}" style="animation-delay: 0.2s;">0</div>
                    <div class="text-gray-600 font-medium">Recent Wins</div>
                </div>
                <div class="neomorphism p-8 text-center hover:scale-105 transition-transform duration-300">
                    <div class="text-4xl mb-4">📊</div>
                    <div class="text-3xl font-bold text-blue-600 animate-score" data-score="{{ $recentResults->count() }}" style="animation-delay: 0.4s;">0</div>
                    <div class="text-gray-600 font-medium">Total Matches</div>
                </div>
                <div class="neomorphism p-8 text-center hover:scale-105 transition-transform duration-300">
                    <div class="text-4xl mb-4">⭐</div>
                    <div class="text-3xl font-bold text-yellow-600 animate-score" data-score="{{ $siteSettings['win_rate'] ?? round(($recentResults->where('score_mbuni', '>', 'score_opponent')->count() / max($recentResults->count(), 1)) * 100) }}" style="animation-delay: 0.6s;">0</div>
                    <div class="text-gray-600 font-medium">Win Rate %</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Filters -->
    <section class="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 py-8 sticky top-20 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                <!-- Competition Filter -->
                <div class="flex flex-wrap gap-3 fade-in-left">
                    <a href="{{ route('fixtures.index') }}"
                       class="px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 hover:scale-105 {{ !request('competition') ? 'bg-gradient-to-r from-primary-600 to-primary-700 text-white shadow-lg' : 'neomorphism text-gray-700 hover:text-primary-600' }}">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                            All Competitions
                        </span>
                    </a>
                    @foreach($competitions as $competition)
                        <a href="{{ route('fixtures.index', ['competition' => $competition]) }}"
                           class="px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 hover:scale-105 {{ request('competition') === $competition ? 'bg-gradient-to-r from-primary-600 to-primary-700 text-white shadow-lg' : 'neomorphism text-gray-700 hover:text-primary-600' }}">
                            {{ $competition }}
                        </a>
                    @endforeach
                </div>

                <!-- Enhanced Status Filter -->
                <div class="flex gap-3 fade-in-right">
                    <a href="{{ route('fixtures.index', array_merge(request()->query(), ['status' => 'upcoming'])) }}"
                       class="px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 hover:scale-105 {{ request('status') === 'upcoming' ? 'bg-gradient-to-r from-green-600 to-green-700 text-white shadow-lg' : 'neomorphism text-gray-700 hover:text-green-600' }}">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Upcoming
                        </span>
                    </a>
                    <a href="{{ route('fixtures.index', array_merge(request()->query(), ['status' => 'finished'])) }}"
                       class="px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 hover:scale-105 {{ request('status') === 'finished' ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg' : 'neomorphism text-gray-700 hover:text-blue-600' }}">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Results
                        </span>
                    </a>
                    <a href="{{ route('fixtures.index') }}"
                       class="px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 hover:scale-105 {{ !request('status') ? 'bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-lg' : 'neomorphism text-gray-700 hover:text-purple-600' }}">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                            </svg>
                            All
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Next Match Highlight -->
    @if($upcomingFixtures->count() > 0 && !request('status'))
        <section class="py-16 bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white relative overflow-hidden">
            <!-- Background Effects -->
            <div class="absolute inset-0">
                <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl"></div>
                <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
            </div>

            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div class="text-center fade-in-up">
                    <h2 class="text-3xl font-bold mb-8 text-yellow-300">⚡ Next Match</h2>

                    <div class="max-w-4xl mx-auto">
                        @php $nextMatch = $upcomingFixtures->first() @endphp
                        <div class="glass-card p-8 hover-lift">
                            <div class="flex items-center justify-between mb-8">
                                <div class="text-center flex-1">
                                    <div class="w-20 h-20 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <span class="text-3xl">{{ $nextMatch->home_or_away === 'home' ? '🏠' : '🏃' }}</span>
                                    </div>
                                    <div class="text-2xl font-bold">{{ $nextMatch->home_or_away === 'home' ? 'Mbuni FC' : $nextMatch->opponent }}</div>
                                    <div class="text-sm text-white/70 mt-1">{{ $nextMatch->home_or_away === 'home' ? 'Home' : 'Away' }}</div>
                                </div>

                                <div class="text-center px-8">
                                    <div class="text-yellow-300 text-sm font-semibold mb-2">{{ $nextMatch->match_date->format('H:i') }}</div>
                                    <div class="text-5xl font-black text-white animate-pulse">VS</div>
                                    <div class="text-yellow-300 text-sm font-semibold mt-2">{{ $nextMatch->match_date->format('M d') }}</div>
                                </div>

                                <div class="text-center flex-1">
                                    <div class="w-20 h-20 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <span class="text-3xl">{{ $nextMatch->home_or_away === 'home' ? '🏃' : '🏠' }}</span>
                                    </div>
                                    <div class="text-2xl font-bold">{{ $nextMatch->home_or_away === 'home' ? $nextMatch->opponent : 'Mbuni FC' }}</div>
                                    <div class="text-sm text-white/70 mt-1">{{ $nextMatch->home_or_away === 'home' ? 'Away' : 'Home' }}</div>
                                </div>
                            </div>

                            <div class="text-center border-t border-white/20 pt-6">
                                <div class="text-xl font-bold mb-2">{{ $nextMatch->match_date->format('l, F d, Y') }}</div>
                                <div class="text-white/80 mb-4">📍 {{ $nextMatch->venue }}</div>
                                <div class="inline-flex items-center px-4 py-2 bg-yellow-400/20 rounded-full text-yellow-300 font-semibold">
                                    🏆 {{ $nextMatch->competition }}
                                </div>

                                <!-- Countdown Timer -->
                                <div class="mt-6 grid grid-cols-4 gap-4 max-w-md mx-auto">
                                    <div class="glass-card p-3 text-center">
                                        <div class="text-2xl font-bold text-yellow-300" id="days">--</div>
                                        <div class="text-xs text-white/70">Days</div>
                                    </div>
                                    <div class="glass-card p-3 text-center">
                                        <div class="text-2xl font-bold text-yellow-300" id="hours">--</div>
                                        <div class="text-xs text-white/70">Hours</div>
                                    </div>
                                    <div class="glass-card p-3 text-center">
                                        <div class="text-2xl font-bold text-yellow-300" id="minutes">--</div>
                                        <div class="text-xs text-white/70">Minutes</div>
                                    </div>
                                    <div class="glass-card p-3 text-center">
                                        <div class="text-2xl font-bold text-yellow-300" id="seconds">--</div>
                                        <div class="text-xs text-white/70">Seconds</div>
                                    </div>
                                </div>

                                <div class="mt-8">
                                    <a href="{{ route('tickets.index') }}" class="btn-primary bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 inline-flex items-center">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5h2.5a2.5 2.5 0 0 1 0 5H5m0 0h2.5a2.5 2.5 0 0 1 0 5H5"></path>
                                        </svg>
                                        Get Tickets Now
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Countdown Script -->
            <script>
                // Set the date we're counting down to
                const countDownDate = new Date("{{ $nextMatch->match_date->format('Y-m-d H:i:s') }}").getTime();

                // Update the count down every 1 second
                const x = setInterval(function() {
                    const now = new Date().getTime();
                    const distance = countDownDate - now;

                    const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                    const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((distance % (1000 * 60)) / 1000);

                    document.getElementById("days").innerHTML = days.toString().padStart(2, '0');
                    document.getElementById("hours").innerHTML = hours.toString().padStart(2, '0');
                    document.getElementById("minutes").innerHTML = minutes.toString().padStart(2, '0');
                    document.getElementById("seconds").innerHTML = seconds.toString().padStart(2, '0');

                    if (distance < 0) {
                        clearInterval(x);
                        document.getElementById("days").innerHTML = "00";
                        document.getElementById("hours").innerHTML = "00";
                        document.getElementById("minutes").innerHTML = "00";
                        document.getElementById("seconds").innerHTML = "00";
                    }
                }, 1000);
            </script>
        </section>
    @endif

    <!-- Enhanced Fixtures List -->
    <section class="py-20 bg-gradient-to-br from-gray-50 to-white relative">
        <!-- Background Decorations -->
        <div class="absolute top-0 right-0 w-72 h-72 md:w-80 md:h-80 lg:w-96 lg:h-96 bg-primary-100 rounded-full blur-3xl opacity-30 transform translate-x-16 md:translate-x-24 lg:translate-x-48 -translate-y-24 md:-translate-y-32 lg:-translate-y-48"></div>
        <div class="absolute bottom-0 left-0 w-64 h-64 md:w-72 md:h-72 lg:w-80 lg:h-80 bg-yellow-100 rounded-full blur-3xl opacity-30 transform -translate-x-16 md:-translate-x-20 lg:-translate-x-40 translate-y-20 md:translate-y-32 lg:translate-y-40"></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            @if($fixtures->count() > 0)
                <!-- Section Header -->
                <div class="text-center mb-12 fade-in-up">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">
                        @if(request('status') === 'upcoming')
                            Upcoming Fixtures
                        @elseif(request('status') === 'finished')
                            Recent Results
                        @else
                            All Fixtures & Results
                        @endif
                    </h2>
                    <p class="text-gray-600">{{ $fixtures->total() }} {{ Str::plural('match', $fixtures->total()) }} found</p>
                </div>

                <!-- Fixed Fixtures Grid - Using masonry layout with proper spacing -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    @foreach($fixtures as $index => $fixture)
                        <div class="w-full fade-in-up" style="animation-delay: {{ $index * 0.1 }}s;">
                            <div class="fixture-card-container relative">
                                <div class="fixture-card glass-card rounded-2xl overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-2">
                                    <!-- Card Header -->
                                    <div class="p-6 border-b border-gray-100">
                                        <div class="flex items-center justify-between">
                                            <span class="badge {{ $fixture->status === 'upcoming' ? 'badge-primary' : ($fixture->status === 'live' ? 'bg-red-500 text-white animate-pulse' : 'badge-success') }}">
                                                {{ $fixture->status === 'live' ? '🔴 LIVE' : ucfirst($fixture->status) }}
                                            </span>
                                            <span class="text-sm text-gray-600 font-medium bg-gray-100 px-3 py-1 rounded-full">{{ $fixture->competition }}</span>
                                        </div>
                                    </div>

                                    <!-- Teams Section -->
                                    <div class="p-6">
                                        <div class="flex items-center justify-between mb-6">
                                            <!-- Home/Away Team -->
                                            <div class="text-center flex-1">
                                                <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                                    <span class="text-2xl">{{ $fixture->home_or_away === 'home' ? '🏠' : '⚽' }}</span>
                                                </div>
                                                <div class="font-bold text-gray-900 text-lg mb-1">
                                                    {{ $fixture->home_or_away === 'home' ? 'Mbuni FC' : $fixture->opponent }}
                                                </div>
                                                <div class="text-xs text-gray-500 uppercase tracking-wide">
                                                    {{ $fixture->home_or_away === 'home' ? 'Home' : 'Away' }}
                                                </div>
                                            </div>

                                            <!-- Score/VS Section -->
                                            <div class="text-center px-6">
                                                @if($fixture->status === 'finished')
                                                    <div class="bg-gray-50 rounded-lg p-4 min-w-[120px]">
                                                        <div class="text-3xl font-black text-primary-600 mb-1">{{ $fixture->result }}</div>
                                                        <div class="text-xs text-gray-500 uppercase">Final Score</div>
                                                    </div>
                                                @else
                                                    <div class="bg-gray-50 rounded-lg p-4 min-w-[120px]">
                                                        <div class="text-2xl font-bold text-gray-400 mb-1">VS</div>
                                                        <div class="text-sm font-semibold text-primary-600">{{ $fixture->match_date->format('H:i') }}</div>
                                                        <div class="text-xs text-gray-500">{{ $fixture->match_date->format('M d') }}</div>
                                                    </div>
                                                @endif
                                            </div>

                                            <!-- Opponent Team -->
                                            <div class="text-center flex-1">
                                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                                    <span class="text-2xl">{{ $fixture->home_or_away === 'home' ? '🏃' : '🏠' }}</span>
                                                </div>
                                                <div class="font-bold text-gray-900 text-lg mb-1">
                                                    {{ $fixture->home_or_away === 'home' ? $fixture->opponent : 'Mbuni FC' }}
                                                </div>
                                                <div class="text-xs text-gray-500 uppercase tracking-wide">
                                                    {{ $fixture->home_or_away === 'home' ? 'Away' : 'Home' }}
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Match Details -->
                                        <div class="border-t border-gray-100 pt-4">
                                            <div class="grid grid-cols-2 gap-4 text-sm">
                                                <div class="text-center">
                                                    <div class="text-gray-500 mb-1">Date</div>
                                                    <div class="font-semibold text-gray-900">{{ $fixture->match_date->format('M d, Y') }}</div>
                                                </div>
                                                <div class="text-center">
                                                    <div class="text-gray-500 mb-1">Venue</div>
                                                    <div class="font-semibold text-gray-900 truncate" title="{{ $fixture->venue }}">{{ $fixture->venue }}</div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Action Button -->
                                        <div class="mt-6 pt-4 border-t border-gray-100">
                                            @if($fixture->status === 'upcoming')
                                                <a href="{{ route('tickets.index') }}" class="w-full btn-primary text-center block py-3">
                                                    <span class="flex items-center justify-center">
                                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5h2.5a2.5 2.5 0 0 1 0 5H5m0 0h2.5a2.5 2.5 0 0 1 0 5H5"></path>
                                                        </svg>
                                                        Get Tickets
                                                    </span>
                                                </a>
                                            @else
                                                <div class="text-center py-3">
                                                    <span class="text-primary-600 font-semibold flex items-center justify-center">
                                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                        </svg>
                                                        Match Completed
                                                    </span>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Enhanced Pagination -->
                <div class="mt-16 fade-in-up">
                    <div class="flex justify-center">
                        {{ $fixtures->appends(request()->query())->links('pagination::tailwind') }}
                    </div>
                </div>
            @else
                <!-- Enhanced Empty State -->
                <div class="text-center py-20 fade-in-up">
                    <div class="max-w-md mx-auto">
                        <!-- Animated Icon -->
                        <div class="relative mb-8">
                            <div class="w-32 h-32 mx-auto bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                                <svg class="h-16 w-16 text-gray-400 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div class="absolute -top-2 -right-2 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm">⚽</span>
                            </div>
                        </div>

                        <h3 class="text-3xl font-bold text-gray-900 mb-4">
                            @if(request('competition') || request('status'))
                                No Matches Found
                            @else
                                No Fixtures Scheduled
                            @endif
                        </h3>

                        <p class="text-lg text-gray-600 mb-8">
                            @if(request('competition') || request('status'))
                                No matches found with the selected filters. Try adjusting your search criteria or view all fixtures.
                            @else
                                Our fixture list is being updated. Check back soon for upcoming matches and exciting football action!
                            @endif
                        </p>

                        <div class="space-y-4">
                            @if(request('competition') || request('status'))
                                <a href="{{ route('fixtures.index') }}" class="btn-primary hover-glow inline-flex items-center">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    View All Fixtures
                                </a>
                            @endif

                            <div class="flex justify-center space-x-4">
                                <a href="{{ route('news.index') }}" class="btn-outline hover:scale-105 transition-transform duration-300">
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                        </svg>
                                        Latest News
                                    </span>
                                </a>
                                <a href="{{ route('squad.index') }}" class="btn-outline hover:scale-105 transition-transform duration-300">
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                        </svg>
                                        Meet the Squad
                                    </span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </section>

    <!-- Match Alerts Section - Properly separated with margin -->
    <section class="py-16 bg-gradient-to-r from-primary-600 to-primary-700 text-white mt-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="fade-in-up">
                <h3 class="text-3xl font-bold mb-4">Never Miss a Match</h3>
                <p class="text-xl text-primary-100 mb-8">Get notified about upcoming fixtures, live scores, and match results</p>

                <form class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                    <input type="email" placeholder="Enter your email address" class="form-input-glow flex-1 text-gray-900">
                    <button type="submit" class="btn-primary bg-yellow-500 hover:bg-yellow-600">
                        <span class="flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2zM4 7h12V5H4v2z"></path>
                            </svg>
                            Subscribe
                        </span>
                    </button>
                </form>

                <p class="text-sm text-primary-200 mt-4">Join 500+ fans getting match alerts. Unsubscribe anytime.</p>
            </div>
        </div>
    </section>
@endsection