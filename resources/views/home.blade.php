@extends('layouts.main')

@section('title', 'Home')
@section('description', 'Welcome to {{ $siteSettings["site_name"] ?? "Mbuni FC" }} - {{ $siteSettings["hero_subtitle"] ?? "The Black Army" }}. Get the latest news, fixtures, and updates.')

@section('content')

    <!-- Animated Logo Intro -->
    <div id="logo-intro" class="fixed inset-0 bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 z-50 flex items-center justify-center">
        <div class="text-center">
            <div class="animate-logo-spin mb-4">
                <img src="{{ asset('images/mbuni-logo.png') }}" alt="Mbuni FC" class="h-24 w-24 mx-auto">
            </div>
            <h1 class="text-4xl font-bold text-white animate-slide-in-up">{{ $siteSettings['site_name'] ?? 'Mbuni FC' }}</h1>
            <p class="text-white/80 animate-slide-in-up" style="animation-delay: 0.3s;">{{ $siteSettings['hero_subtitle'] ?? 'The Black Army' }}</p>
        </div>
    </div>

    <!-- Hero Section with Video Background -->
    <section class="relative min-h-screen flex items-center justify-center overflow-hidden cursor-ripple" id="hero-section">
        <!-- Video Background -->
        <div class="absolute inset-0 z-0">
            <div class="hero-gradient-animated absolute inset-0"></div>
            <!-- Fallback gradient if no video -->
            <div class="absolute inset-0 bg-gradient-to-br from-primary-600/90 via-primary-700/90 to-primary-800/90"></div>

            <!-- Animated particles -->
            <div class="absolute inset-0">
                <div class="particle particle-1">⚽</div>
                <div class="particle particle-2">⚽</div>
                <div class="particle particle-3">⚽</div>
                <div class="particle particle-4">⚽</div>
                <div class="particle particle-5">⚽</div>
                <div class="particle particle-6">⚽</div>
            </div>
        </div>

        <!-- Hero Content -->
        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-4 sm:mb-8">
                <!-- Left Content -->
                <div class="fade-in-left">
                    <h1 class="text-5xl md:text-7xl sm:text-3xl font-black mb-6 mt-8 leading-tight">
                        {{ $siteSettings['hero_title'] ?? 'Welcome to' }}
                        <span class="text-gradient bg-gradient-to-r from-yellow-300 via-yellow-400 to-yellow-500 bg-clip-text text-transparent">
                            {{ $siteSettings['site_name'] ?? 'Mbuni FC' }}
                        </span>
                    </h1>
                    <p class="text-xl md:text-2xl mb-8 text-white/90 leading-relaxed">
                        {{ $siteSettings['hero_subtitle'] ?? "Arusha's premier football club, The Black Army." }}
                    </p>

                    <!-- Next Match Card with Glassmorphism -->
                    @if($nextFixture)
                        <div class="glass-card p-6 mb-8 fade-in-up" style="animation-delay: 0.3s;">
                            <h3 class="text-lg font-semibold mb-4 text-yellow-300">🏆 Next Match</h3>
                            <div class="flex items-center justify-between">
                                <div class="text-left">
                                    <p class="text-2xl font-bold">{{ $nextFixture->home_or_away === 'home' ? $siteSettings['site_name'] ?? 'Our Team' : $nextFixture->opponent }}</p>
                                    <p class="text-sm text-white/70">{{ $nextFixture->home_or_away === 'home' ? 'vs' : 'vs' }}</p>
                                    <p class="text-2xl font-bold">{{ $nextFixture->home_or_away === 'home' ? $nextFixture->opponent : $siteSettings['site_name'] ?? 'Our Team' }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-lg font-semibold text-yellow-300">{{ $nextFixture->match_date->format('M d, Y') }}</p>
                                    <p class="text-sm text-white/70">{{ $nextFixture->match_date->format('H:i') }}</p>
                                    <p class="text-sm text-white/70">📍 {{ $nextFixture->venue }}</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- CTA Buttons with Enhanced Effects -->
                    <div class="flex flex-col sm:flex-row gap-4 fade-in-up" style="animation-delay: 0.6s;">
                        <a href="{{ route('tickets.index') }}" class="btn-primary bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-center ripple hover-glow transform hover:scale-105 transition-all duration-300">
                            🎫 Get Tickets
                        </a>
                        <a href="{{ route('news.index') }}" class="glass border-2 border-white/30 text-white hover:bg-white/20 text-center py-3 px-6 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
                            📰 Latest News
                        </a>
                        <a href="{{ route('squad.index') }}" class="glass border-2 border-yellow-300/50 text-yellow-300 hover:bg-yellow-300/20 text-center py-3 px-6 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
                            ⚽ Meet the Squad
                        </a>
                    </div>
                </div>

                <!-- Right Content - Enhanced Image -->
                <div class="relative fade-in-right" style="animation-delay: 0.4s;">
                    <div class="relative group">
                        <!-- Main Image with Enhanced Effects -->
                        <div class="image-zoom relative overflow-hidden rounded-2xl shadow-2xl">
                            <img src="{{ asset('images/mbuni-logo.png') }}" alt="Mbuni FC Team" class="w-full h-96 object-cover transition-transform duration-700 group-hover:scale-110">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>

                            <!-- Floating Stats -->
                            <div class="absolute bottom-4 left-4 glass-card p-3 float">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-yellow-300">{{ date('Y') - $siteSettings['founded_year'] ?? '15' }}+</div>
                                    <div class="text-xs text-white/80">Years</div>
                                </div>
                            </div>

                            <div class="absolute top-4 right-4 glass-card p-3 float" style="animation-delay: 1s;">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-yellow-300">{{ $siteSettings['total_fans'] ?? '1000' }}+</div>
                                    <div class="text-xs text-white/80">Fans</div>
                                </div>
                            </div>

                            <div class="absolute bottom-4 right-4 glass-card p-3 float" style="animation-delay: 2s;">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-yellow-300">{{ $siteSettings['total_wins'] ?? '100' }}+</div>
                                    <div class="text-xs text-white/80">Wins</div>
                                </div>
                            </div>
                        </div>

                        <!-- Decorative Elements -->
                        <div class="absolute -top-4 -left-4 w-24 h-24 bg-yellow-400/20 rounded-full blur-xl animate-pulse"></div>
                        <div class="absolute -bottom-4 -right-4 w-32 h-32 bg-primary-400/20 rounded-full blur-xl animate-pulse" style="animation-delay: 1s;"></div>
                    </div>
                </div>
            </div>

            <!-- Scroll Indicator -->
            <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
                <div class="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
                    <div class="w-1 h-3 bg-white/70 rounded-full mt-2 animate-pulse"></div>
                </div>
                <p class="text-white/60 text-sm mt-2">Scroll Down</p>
            </div>
        </div>
    </section>

    <!-- Latest News Section -->
    <section class="py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
        <!-- Background Decorations -->
        <div class="absolute top-0 left-0 w-64 h-64 bg-primary-100 rounded-full blur-3xl opacity-30 -translate-x-32 -translate-y-32"></div>
        <div class="absolute bottom-0 right-0 w-96 h-96 bg-yellow-100 rounded-full blur-3xl opacity-30 translate-x-48 translate-y-48"></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center mb-16 fade-in-up">
                <h2 class="text-4xl md:text-5xl font-black text-gray-900 mb-6">
                    Latest <span class="text-gradient">News</span>
                </h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    Stay updated with the latest from {{ $siteSettings['site_name'] ?? 'Mbuni FC' }} - match reports, transfers, and exclusive behind-the-scenes content
                </p>
            </div>

            @if($latestNews->count() > 0)
                <!-- Featured News Carousel -->
                <div class="carousel-wrapper mb-12 fade-in-up" style="animation-delay: 0.2s;">
                    <div class="relative">
                        <div class="carousel-container flex gap-6 overflow-x-auto pb-4 snap-x snap-mandatory scrollbar-hide">
                            @foreach($latestNews as $index => $article)
                                <article class="carousel-item min-w-80 md:min-w-96 card-modern hover-lift group {{ $index === 0 ? 'featured-card' : '' }}">
                                    <div class="relative overflow-hidden">
                                        @if($article->image)
                                            <div class="image-zoom h-56 overflow-hidden">
                                                <img src="{{ $article->image_url }}" alt="{{ $article->title }}" class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">
                                                <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                            </div>
                                        @else
                                            <div class="h-56 bg-gradient-to-br from-primary-500 to-primary-700 flex items-center justify-center">
                                                <div class="text-white text-6xl">📰</div>
                                            </div>
                                        @endif

                                        <!-- Floating Badge -->
                                        <div class="absolute top-4 left-4">
                                            <span class="badge badge-primary glass-card text-white font-semibold">
                                                {{ $article->category_name }}
                                            </span>
                                        </div>

                                        <!-- Date Badge -->
                                        <div class="absolute top-4 right-4">
                                            <div class="glass-card px-3 py-1 text-white text-sm font-medium">
                                                {{ $article->published_at->format('M d') }}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="p-6">
                                        <h3 class="text-xl font-bold mb-3 text-gray-900 group-hover:text-primary-600 transition-colors duration-300 line-clamp-2">
                                            <a href="{{ route('news.show', $article) }}" class="hover:underline">
                                                {{ $article->title }}
                                            </a>
                                        </h3>
                                        <p class="text-gray-600 mb-4 line-clamp-3">{{ $article->excerpt }}</p>

                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-2">
                                                <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                                                    <span class="text-primary-600 font-semibold text-sm">
                                                        {{ substr($article->author->name, 0, 1) }}
                                                    </span>
                                                </div>
                                                <span class="text-sm text-gray-500">{{ $article->author->name }}</span>
                                            </div>
                                            <a href="{{ route('news.show', $article) }}" class="inline-flex items-center text-primary-600 hover:text-primary-700 font-semibold text-sm group-hover:translate-x-1 transition-transform duration-300">
                                                Read More
                                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                </svg>
                                            </a>
                                        </div>
                                    </div>
                                </article>
                            @endforeach
                        </div>

                        <!-- Carousel Navigation -->
                        <button class="carousel-prev absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 w-12 h-12 bg-white shadow-lg rounded-full flex items-center justify-center hover:bg-primary-50 transition-colors duration-300">
                            <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                        </button>
                        <button class="carousel-next absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 w-12 h-12 bg-white shadow-lg rounded-full flex items-center justify-center hover:bg-primary-50 transition-colors duration-300">
                            <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12 fade-in-up" style="animation-delay: 0.4s;">
                    <div class="neomorphism p-6 text-center hover:scale-105 transition-transform duration-300">
                        <div class="text-3xl font-bold text-primary-600 animate-score" data-score="{{ $siteSettings['total_articles'] ?? '150' }}">0</div>
                        <div class="text-sm text-gray-600 font-medium">News Articles</div>
                    </div>
                    <div class="neomorphism p-6 text-center hover:scale-105 transition-transform duration-300">
                        <div class="text-3xl font-bold text-primary-600 animate-score" data-score="{{ $siteSettings['total_reports'] ?? '25' }}">0</div>
                        <div class="text-sm text-gray-600 font-medium">Match Reports</div>
                    </div>
                    <div class="neomorphism p-6 text-center hover:scale-105 transition-transform duration-300">
                        <div class="text-3xl font-bold text-primary-600 animate-score" data-score="{{ $siteSettings['total_readers'] ?? '500' }}">0</div>
                        <div class="text-sm text-gray-600 font-medium">Readers</div>
                    </div>
                    <div class="neomorphism p-6 text-center hover:scale-105 transition-transform duration-300">
                        <div class="text-3xl font-bold text-primary-600 animate-score" data-score="{{ $siteSettings['total_categories'] ?? '12' }}">0</div>
                        <div class="text-sm text-gray-600 font-medium">Categories</div>
                    </div>
                </div>

                <div class="text-center fade-in-up" style="animation-delay: 0.6s;">
                    <a href="{{ route('news.index') }}" class="btn-primary hover-glow inline-flex items-center">
                        <span>View All News</span>
                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                        </svg>
                    </a>
                </div>
            @else
                <div class="text-center py-16 fade-in-up">
                    <div class="text-6xl mb-4">📰</div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">No News Yet</h3>
                    <p class="text-gray-500 text-lg">Check back soon for the latest updates from Mbuni FC!</p>
                </div>
            @endif
        </div>
    </section>

    <!-- Match Center Section -->
    @if($recentResults->count() > 0 || $nextFixture)
        <section class="py-20 bg-gradient-to-br from-primary-900 via-primary-800 to-primary-900 text-white relative overflow-hidden">
            <!-- Background Effects -->
            <div class="absolute inset-0">
                <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl"></div>
                <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
            </div>

            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div class="text-center mb-16 fade-in-up">
                    <h2 class="text-4xl md:text-5xl font-black mb-6">
                        Match <span class="text-yellow-300">Center</span>
                    </h2>
                    <p class="text-xl text-white/80 max-w-2xl mx-auto">
                        Follow our journey through victories, challenges, and unforgettable moments on the pitch
                    </p>
                </div>

                <!-- Recent Results with Glassmorphism -->
                @if($recentResults->count() > 0)
                    <div class="mb-16 fade-in-up" style="animation-delay: 0.2s;">
                        <h3 class="text-2xl font-bold text-center mb-8 text-yellow-300">🏆 Recent Results</h3>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            @foreach($recentResults as $index => $result)
                                <div class="flip-card group">
                                    <div class="flip-card-inner">
                                        <!-- Front of Card -->
                                        <div class="flip-card-front glass-card p-6 text-center">
                                            <div class="mb-4">
                                                <div class="text-sm text-yellow-300 font-semibold mb-1">{{ $result->competition }}</div>
                                                <div class="text-sm text-white/70">{{ $result->match_date->format('M d, Y') }}</div>
                                            </div>

                                            <div class="flex items-center justify-between mb-6">
                                                <div class="text-center flex-1">
                                                    <div class="w-12 h-12 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-2">
                                                        <span class="text-lg">⚽</span>
                                                    </div>
                                                    <div class="font-bold text-sm">{{ $result->home_or_away === 'home' ? ($siteSettings['site_name'] ?? 'Mbuni FC') : $result->opponent }}</div>
                                                </div>

                                                <div class="text-center px-4">
                                                    <div class="text-3xl font-black text-yellow-300 animate-score" data-score="{{ explode('-', $result->result)[0] ?? 0 }}">0</div>
                                                    <div class="text-white/50 text-xs">-</div>
                                                    <div class="text-3xl font-black text-yellow-300 animate-score" data-score="{{ explode('-', $result->result)[1] ?? 0 }}" style="animation-delay: 0.5s;">0</div>
                                                </div>

                                                <div class="text-center flex-1">
                                                    <div class="w-12 h-12 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-2">
                                                        <span class="text-lg">🏃</span>
                                                    </div>
                                                    <div class="font-bold text-sm">{{ $result->home_or_away === 'home' ? $result->opponent : ($siteSettings['site_name'] ?? 'Mbuni FC') }}</div>
                                                </div>
                                            </div>

                                            <div class="text-sm text-white/70">📍 {{ $result->venue }}</div>
                                            <div class="text-xs text-yellow-300 mt-2">Hover for details</div>
                                        </div>

                                        <!-- Back of Card -->
                                        <div class="flip-card-back glass-card p-6 text-center bg-gradient-to-br from-primary-600/80 to-primary-800/80">
                                            <h4 class="text-lg font-bold mb-4 text-yellow-300">Match Stats</h4>
                                            <div class="space-y-3 text-sm">
                                                <div class="flex justify-between">
                                                    <span>Competition:</span>
                                                    <span class="font-semibold">{{ $result->competition }}</span>
                                                </div>
                                                <div class="flex justify-between">
                                                    <span>Venue:</span>
                                                    <span class="font-semibold">{{ $result->venue }}</span>
                                                </div>
                                                <div class="flex justify-between">
                                                    <span>Date:</span>
                                                    <span class="font-semibold">{{ $result->match_date->format('M d, Y') }}</span>
                                                </div>
                                                <div class="flex justify-between">
                                                    <span>Result:</span>
                                                    <span class="font-bold text-yellow-300">{{ $result->result }}</span>
                                                </div>
                                            </div>
                                            <div class="mt-4 pt-4 border-t border-white/20">
                                                <a href="{{ route('fixtures.index') }}" class="text-yellow-300 hover:text-yellow-200 text-sm font-semibold">
                                                    View Full Report →
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Next Fixture Highlight -->
                @if($nextFixture)
                    <div class="text-center fade-in-up" style="animation-delay: 0.4s; margin-top: 20rem;">
                        <h3 class="text-2xl font-bold mb-8 text-yellow-300">⚡ Next Match</h3>

                        <div class="max-w-2xl mx-auto">
                            <div class="glass-card p-8 hover-lift">
                                <div class="flex items-center justify-between mb-6">
                                    <div class="text-center flex-1">
                                        <div class="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-3">
                                            <span class="text-2xl">🏠</span>
                                        </div>
                                        <div class="font-bold text-lg">{{ $nextFixture->home_or_away === 'home' ? 'Mbuni FC' : $nextFixture->opponent }}</div>
                                    </div>

                                    <div class="text-center px-6">
                                        <div class="text-yellow-300 text-sm font-semibold mb-2">{{ $nextFixture->match_date->format('H:i') }}</div>
                                        <div class="text-4xl font-black text-white">VS</div>
                                        <div class="text-yellow-300 text-sm font-semibold mt-2">{{ $nextFixture->match_date->format('M d') }}</div>
                                    </div>

                                    <div class="text-center flex-1">
                                        <div class="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-3">
                                            <span class="text-2xl">🏃</span>
                                        </div>
                                        <div class="font-bold text-lg">{{ $nextFixture->home_or_away === 'home' ? $nextFixture->opponent : 'Mbuni FC' }}</div>
                                    </div>
                                </div>

                                <div class="text-center">
                                    <div class="text-white/70 mb-4">📍 {{ $nextFixture->venue }}</div>
                                    <div class="inline-flex items-center px-4 py-2 bg-yellow-400/20 rounded-full text-yellow-300 font-semibold">{{ $nextFixture->competition }}</div>

                                    <!-- <a href="{{ route('tickets.index') }}" class="btn-primary bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 inline-flex items-center">
                                        <span>Get Tickets</span>
                                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5h2.5a2.5 2.5 0 0 1 0 5H5m0 0h2.5a2.5 2.5 0 0 1 0 5H5"></path>
                                        </svg>
                                    </a> -->
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <div class="text-center mt-12 fade-in-up" style="animation-delay: 0.6s;">
                    <a href="{{ route('fixtures.index') }}" class="btn-primary bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 inline-flex items-center">
                        <span>View All Fixtures</span>
                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </section>
    @endif

    <!-- Sponsors Section -->
    @if($sponsors->count() > 0)
        <section class="py-16 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Partners</h2>
                    <p class="text-xl text-gray-600">Proud to be supported by these amazing partners</p>
                </div>

                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center">
                    @foreach($sponsors as $sponsor)
                        <div class="text-center">
                            @if($sponsor->has_website)
                                <a href="{{ $sponsor->formatted_website_url }}" target="_blank" class="block hover:opacity-75 transition-opacity">
                                    <img src="{{ $sponsor->logo_url }}" alt="{{ $sponsor->name }}" class="h-16 mx-auto object-contain">
                                    <span class="text-sm text-gray-700">
                                        {{ $sponsor->name }}
                                    </span>
                                </a>
                            @else
                                <img src="{{ $sponsor->logo_url }}" alt="{{ $sponsor->name }}" class="h-16 mx-auto object-contain">
                                <span class="text-sm text-gray-500">
                                    {{ $sponsor->name }}
                                </span>
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    @endif

    <!-- Call to Action Section -->
    <section class="py-20 bg-gradient-to-br from-primary-900 via-primary-800 to-primary-900 text-white relative overflow-hidden">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">Join the Mbuni Family</h2>
            <p class="text-xl mb-8 text-gray-100">
                Be part of our journey and support your local team. Get exclusive merchandise, match tickets, and stay connected with the club.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('store.index') }}" class="btn-primary bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 inline-flex items-center">
                    Shop Merchandise
                </a>
                <a href="{{ route('contact.index') }}" class="btn-primary bg-yellow-500 hover:bg-yellow-600">
                    Contact Us
                </a>
            </div>
        </div>
    </section>
@endsection