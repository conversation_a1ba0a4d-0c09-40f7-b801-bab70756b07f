@extends('layouts.main')

@section('title', $merchandise->name . ' - Store')
@section('description', $merchandise->description)

@section('content')
    <!-- Enhanced Product Header -->
    <section class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white py-20 overflow-hidden">
        <!-- Background Effects -->
        <div class="absolute inset-0">
            <!-- <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl"></div>
            <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div> -->
        </div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <!-- Breadcrumb -->
            <nav class="flex mb-8 fade-in-up" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="{{ route('home') }}" class="text-white/70 hover:text-white transition-colors duration-200">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-white/50" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <a href="{{ route('store.index') }}" class="ml-1 text-white/70 hover:text-white transition-colors duration-200 md:ml-2">Store</a>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-white/50" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="ml-1 text-white md:ml-2">{{ $merchandise->name }}</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <div class="text-center fade-in-up">
                <h1 class="text-4xl md:text-5xl font-black mb-4">
                    {{ $merchandise->name }}
                </h1>
                <p class="text-xl text-white/90 max-w-2xl mx-auto">
                    Official Mbuni FC merchandise - Show your support with pride
                </p>
            </div>
        </div>
    </section>

    <!-- Product Details -->
    <section class="py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
        <!-- Background Decorations -->
        <div class="absolute top-0 right-0 w-96 h-96 bg-primary-100 rounded-full blur-3xl opacity-30 translate-x-48 -translate-y-48"></div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Product Image -->
                <div class="fade-in-left">
                    <div class="tilt-3d group">
                        @if($merchandise->image)
                            <div class="image-zoom overflow-hidden rounded-2xl shadow-2xl">
                                <img src="{{ $merchandise->image_url }}" 
                                     alt="{{ $merchandise->name }}" 
                                     class="w-full h-96 lg:h-[500px] object-cover transition-transform duration-700 group-hover:scale-110">
                            </div>
                        @else
                            <div class="w-full h-96 lg:h-[500px] bg-gradient-to-br from-primary-500 via-primary-600 to-primary-700 flex items-center justify-center rounded-2xl shadow-2xl">
                                <div class="text-center text-white">
                                    <svg class="w-24 h-24 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                    </svg>
                                    <p class="text-lg font-semibold">{{ $merchandise->name }}</p>
                                </div>
                            </div>
                        @endif
                        
                        <!-- Floating Badge -->
                        @if($merchandise->category)
                            <div class="absolute top-4 left-4">
                                <span class="glass-card px-4 py-2 text-white font-semibold">
                                    {{ ucfirst($merchandise->category) }}
                                </span>
                            </div>
                        @endif
                        
                        <!-- Stock Status -->
                        <div class="absolute top-4 right-4">
                            @if($merchandise->stock_quantity > 0)
                                <span class="glass-card px-4 py-2 text-green-400 font-semibold">
                                    ✓ In Stock
                                </span>
                            @else
                                <span class="glass-card px-4 py-2 text-red-400 font-semibold">
                                    ✗ Out of Stock
                                </span>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Product Info -->
                <div class="fade-in-right">
                    <div class="space-y-6">
                        <!-- Title and Category -->
                        <div>
                            <h2 class="text-3xl font-black text-gray-900 mb-2">{{ $merchandise->name }}</h2>
                            @if($merchandise->category)
                                <span class="badge badge-primary text-lg px-4 py-2">{{ ucfirst($merchandise->category) }}</span>
                            @endif
                        </div>

                        <!-- Description -->
                        <div class="glass-card p-6 rounded-2xl">
                            <h3 class="text-xl font-bold text-gray-900 mb-4 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Description
                            </h3>
                            <p class="text-gray-700 leading-relaxed">{{ $merchandise->description }}</p>
                        </div>

                        <!-- Stock Info -->
                        <div class="neomorphism p-6">
                            <h3 class="text-lg font-bold text-gray-900 mb-4">Availability</h3>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600">Stock Status:</span>
                                @if($merchandise->stock_quantity > 0)
                                    <span class="text-green-600 font-semibold flex items-center">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        Available ({{ $merchandise->stock_quantity }} items)
                                    </span>
                                @else
                                    <span class="text-red-600 font-semibold flex items-center">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                        Out of Stock
                                    </span>
                                @endif
                            </div>
                        </div>

                        <!-- Contact for Availability -->
                        <div class="space-y-4">
                            <h3 class="text-xl font-bold text-gray-900">Interested in this item?</h3>
                            <p class="text-gray-600">Contact us for availability and more information about this official Mbuni FC merchandise.</p>
                            
                            <div class="flex flex-col sm:flex-row gap-4">
                                <a href="{{ route('contact.index') }}" class="btn-primary hover-glow flex-1 text-center">
                                    <span class="flex items-center justify-center">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                        Contact Us
                                    </span>
                                </a>
                                <button class="btn-outline hover:scale-105 transition-transform duration-300 flex-1" onclick="shareProduct()">
                                    <span class="flex items-center justify-center">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                        </svg>
                                        Share
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Products -->
    @if($relatedProducts->count() > 0)
        <section class="py-16 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12 fade-in-up">
                    <h3 class="text-3xl font-bold text-gray-900 mb-4">
                        Related <span class="text-gradient">Products</span>
                    </h3>
                    <p class="text-gray-600">More official Mbuni FC merchandise you might like</p>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                    @foreach($relatedProducts as $index => $item)
                        <div class="card-modern hover-lift group fade-in-up" style="animation-delay: {{ $index * 0.1 }}s;">
                            <div class="relative overflow-hidden">
                                @if($item->image)
                                    <div class="image-zoom h-48 overflow-hidden">
                                        <img src="{{ $item->image_url }}" 
                                             alt="{{ $item->name }}" 
                                             class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">
                                    </div>
                                @else
                                    <div class="h-48 bg-gradient-to-br from-primary-500 to-primary-700 flex items-center justify-center">
                                        <svg class="w-12 h-12 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                        </svg>
                                    </div>
                                @endif
                                
                                <!-- Category Badge -->
                                @if($item->category)
                                    <div class="absolute top-2 left-2">
                                        <span class="glass-card px-2 py-1 text-white text-xs font-semibold">
                                            {{ ucfirst($item->category) }}
                                        </span>
                                    </div>
                                @endif
                            </div>
                            
                            <div class="p-4">
                                <h4 class="font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors duration-300">
                                    {{ $item->name }}
                                </h4>
                                <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ $item->description }}</p>
                                
                                <a href="{{ route('store.show', $item) }}" class="btn-outline w-full text-center hover:scale-105 transition-transform duration-300">
                                    View Details
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    @endif
@endsection

@push('scripts')
<script>
    function shareProduct() {
        if (navigator.share) {
            navigator.share({
                title: '{{ $merchandise->name }} - Mbuni FC Store',
                text: '{{ $merchandise->description }}',
                url: window.location.href
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(window.location.href).then(() => {
                alert('Product link copied to clipboard!');
            });
        }
    }
</script>
@endpush
