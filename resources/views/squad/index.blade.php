@extends('layouts.main')

@section('title', 'Squad')
@section('description', 'Meet the Mbuni FC squad. View player profiles, positions, and statistics of our talented team.')

@section('content')
    <!-- Enhanced Page Header -->
    <section class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white py-20 -mt-20 overflow-hidden">
        <!-- Background Effects -->
        <div class="absolute inset-0">
            <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl"></div>
            <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
            <!-- Animated Player Icons -->
            <div class="absolute top-10 left-10 text-white/10 text-4xl animate-bounce">⚽</div>
            <div class="absolute top-20 right-20 text-white/10 text-3xl animate-bounce" style="animation-delay: 1s;">👕</div>
            <div class="absolute bottom-20 left-20 text-white/10 text-3xl animate-bounce" style="animation-delay: 2s;">🏃</div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center fade-in-up">
                <h1 class="text-5xl md:text-6xl font-black mb-6">
                    Our <span class="text-gradient bg-gradient-to-r from-yellow-300 to-yellow-500 bg-clip-text text-transparent">Squad</span>
                </h1>
                <p class="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed mb-8">
                    Meet the talented athletes who wear the Mbuni FC colors with pride and passion
                </p>

                <!-- Squad Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto">
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="{{ $siteSettings['total_players'] ?? '25' }}">0</div>
                        <div class="text-sm text-white/70">Players</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="{{ $siteSettings['total_nationalities'] ?? '8' }}">0</div>
                        <div class="text-sm text-white/70">Nationalities</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="{{ $siteSettings['average_age'] ?? '24' }}">0</div>
                        <div class="text-sm text-white/70">Avg Age</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="{{ $siteSettings['years_experience'] ?? '15' }}">0</div>
                        <div class="text-sm text-white/70">Years Exp</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Position Filter -->
    <section class="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 py-8 sticky top-20 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-wrap gap-4 justify-center fade-in-up">
                <a href="{{ route('squad.index') }}"
                   class="px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 hover:scale-105 {{ !request('position') ? 'bg-gradient-to-r from-primary-600 to-primary-700 text-white shadow-lg' : 'neomorphism text-gray-700 hover:text-primary-600' }}">
                    <span class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        All Players
                    </span>
                </a>
                @foreach($positions as $key => $name)
                    <a href="{{ route('squad.index', ['position' => $key]) }}"
                       class="px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 hover:scale-105 {{ request('position') === $key ? 'bg-gradient-to-r from-primary-600 to-primary-700 text-white shadow-lg' : 'neomorphism text-gray-700 hover:text-primary-600' }}">
                        <span class="flex items-center">
                            @if($key === 'goalkeeper')
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                </svg>
                            @elseif($key === 'defender')
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.618 5.984A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                </svg>
                            @elseif($key === 'midfielder')
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            @else
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3l14 9-14 9V3z"></path>
                                </svg>
                            @endif
                            {{ $name }}
                        </span>
                    </a>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Enhanced Squad Grid -->
    <section class="py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
        <!-- Background Decorations -->
        <div class="absolute top-0 right-0 w-96 h-96 bg-primary-100 rounded-full blur-3xl opacity-30 translate-x-48 -translate-y-48"></div>
        <div class="absolute bottom-0 left-0 w-80 h-80 bg-yellow-100 rounded-full blur-3xl opacity-30 -translate-x-40 translate-y-40"></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            @if($playersByPosition->count() > 0)
                @foreach($positions as $position => $positionName)
                    @if($playersByPosition->has($position) && (!request('position') || request('position') === $position))
                        <div class="mb-16 fade-in-up">
                            <!-- Position Header -->
                            <div class="text-center mb-12">
                                <h2 class="text-4xl font-black text-gray-900 mb-4">
                                    <span class="text-gradient">{{ $positionName }}</span>
                                </h2>
                                <div class="w-24 h-1 bg-gradient-to-r from-primary-500 to-primary-700 mx-auto rounded-full"></div>
                            </div>

                            <!-- Players Grid with Masonry Layout -->
                            <div class="masonry-grid">
                                @foreach($playersByPosition[$position] as $index => $player)
                                    <div class="masonry-item fade-in-up" style="animation-delay: {{ $index * 0.1 }}s;">
                                        <div class="neomorphism hover:scale-105 transition-all duration-500 group cursor-pointer relative overflow-hidden"
                                             onclick="openPlayerModal({{ $player->id }})" data-modal-open="player-modal-{{ $player->id }}">

                                            <!-- Player Photo -->
                                            <div class="relative overflow-hidden">
                                                @if($player->photo)
                                                    <div class="image-zoom h-80 overflow-hidden">
                                                        <img src="{{ $player->photo_url }}" alt="{{ $player->name }}" class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">
                                                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                                    </div>
                                                @else
                                                    <div class="h-80 bg-gradient-to-br from-primary-500 via-primary-600 to-primary-700 flex items-center justify-center relative overflow-hidden group-hover:from-primary-600 group-hover:to-primary-800 transition-colors duration-300">
                                                        <div class="absolute inset-0 bg-gradient-to-br from-transparent to-black/20"></div>
                                                        <svg class="h-32 w-32 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                        </svg>
                                                    </div>
                                                @endif

                                                <!-- Jersey Number with Neumorphism -->
                                                <div class="absolute top-4 right-4 neomorphism w-16 h-16 flex items-center justify-center bg-white">
                                                    <span class="text-2xl font-black text-primary-600">{{ $player->shirt_number }}</span>
                                                </div>

                                                <!-- Position Badge -->
                                                <div class="absolute top-4 left-4">
                                                    <span class="glass-card px-3 py-1 text-white text-sm font-semibold">
                                                        {{ $player->position_name }}
                                                    </span>
                                                </div>

                                                <!-- Hover Stats Overlay -->
                                                @if($player->stats)
                                                    <div class="absolute inset-0 bg-gradient-to-t from-primary-900/90 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                                                        <div class="p-6 w-full">
                                                            <div class="grid grid-cols-3 gap-4 text-center text-white">
                                                                @if(isset($player->stats['goals']))
                                                                    <div>
                                                                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="{{ $player->stats['goals'] }}">0</div>
                                                                        <div class="text-xs">Goals</div>
                                                                    </div>
                                                                @endif
                                                                @if(isset($player->stats['assists']))
                                                                    <div>
                                                                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="{{ $player->stats['assists'] }}" style="animation-delay: 0.2s;">0</div>
                                                                        <div class="text-xs">Assists</div>
                                                                    </div>
                                                                @endif
                                                                @if(isset($player->stats['appearances']))
                                                                    <div>
                                                                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="{{ $player->stats['appearances'] }}" style="animation-delay: 0.4s;">0</div>
                                                                        <div class="text-xs">Apps</div>
                                                                    </div>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endif
                                            </div>

                                            <!-- Player Info -->
                                            <div class="p-6 text-center">
                                                <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors duration-300">
                                                    {{ $player->name }}
                                                </h3>
                                                <p class="text-primary-600 font-semibold mb-2">{{ $player->position_name }}</p>
                                                <div class="flex items-center justify-center space-x-2 text-gray-500 text-sm">
                                                    <span class="flag-icon">🌍</span>
                                                    <span>{{ $player->nationality }}</span>
                                                </div>

                                                <!-- Quick Stats (Always Visible) -->
                                                @if($player->stats)
                                                    <div class="mt-4 pt-4 border-t border-gray-200">
                                                        <div class="grid grid-cols-3 gap-2 text-center">
                                                            @if(isset($player->stats['goals']))
                                                                <div>
                                                                    <div class="text-lg font-bold text-primary-600">{{ $player->stats['goals'] }}</div>
                                                                    <div class="text-xs text-gray-500">Goals</div>
                                                                </div>
                                                            @endif
                                                            @if(isset($player->stats['assists']))
                                                                <div>
                                                                    <div class="text-lg font-bold text-primary-600">{{ $player->stats['assists'] }}</div>
                                                                    <div class="text-xs text-gray-500">Assists</div>
                                                                </div>
                                                            @endif
                                                            @if(isset($player->stats['appearances']))
                                                                <div>
                                                                    <div class="text-lg font-bold text-primary-600">{{ $player->stats['appearances'] }}</div>
                                                                    <div class="text-xs text-gray-500">Apps</div>
                                                                </div>
                                                            @endif
                                                        </div>
                                                    </div>
                                                @endif

                                                <!-- Click to View More -->
                                                <div class="mt-4 text-xs text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                                    Click to view full profile
                                                </div>
                                            </div>

                                            <!-- Hover Glow Effect -->
                                            <div class="absolute inset-0 bg-gradient-to-t from-primary-600/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none rounded-2xl"></div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                @endforeach
            @else
                <!-- Enhanced Empty State -->
                <div class="text-center py-20 fade-in-up">
                    <div class="max-w-md mx-auto">
                        <!-- Animated Icon -->
                        <div class="relative mb-8">
                            <div class="w-32 h-32 mx-auto bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                                <svg class="h-16 w-16 text-gray-400 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                            </div>
                            <div class="absolute -top-2 -right-2 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm">👕</span>
                            </div>
                        </div>

                        <h3 class="text-3xl font-bold text-gray-900 mb-4">No Players Found</h3>
                        <p class="text-lg text-gray-600 mb-8">
                            @if(request('position'))
                                No players are currently registered in the selected position. Our squad is constantly evolving!
                            @else
                                Our squad information is being updated. Check back soon to meet our talented players!
                            @endif
                        </p>

                        <div class="space-y-4">
                            @if(request('position'))
                                <a href="{{ route('squad.index') }}" class="btn-primary hover-glow inline-flex items-center">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                    View All Players
                                </a>
                            @endif

                            <div class="flex justify-center space-x-4">
                                <a href="{{ route('fixtures.index') }}" class="btn-outline hover:scale-105 transition-transform duration-300">
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        View Fixtures
                                    </span>
                                </a>
                                <a href="{{ route('news.index') }}" class="btn-outline hover:scale-105 transition-transform duration-300">
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                        </svg>
                                        Latest News
                                    </span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </section>

    <!-- Enhanced Player Modal -->
    <div id="playerModal" class="modal-backdrop hidden" onclick="closePlayerModal(event)">
        <div class="modal-content max-w-4xl w-full" onclick="event.stopPropagation()">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200">
                <h3 class="text-2xl font-bold text-gray-900">Player Profile</h3>
                <button onclick="closePlayerModal()" class="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200">
                    <svg class="w-6 h-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Modal Content -->
            <div id="modalContent" class="p-6">
                <!-- Loading State -->
                <div class="flex items-center justify-center py-12">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Fan Engagement Section -->
    <!-- <section class="py-16 bg-gradient-to-r from-primary-600 to-primary-700 text-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="fade-in-up">
                <h3 class="text-3xl font-bold mb-4">Support Our Players</h3>
                <p class="text-xl text-primary-100 mb-8">Follow your favorite players and get exclusive updates on their performance</p>

                <div class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                    <input type="email" placeholder="Enter your email address" class="form-input-glow flex-1 text-gray-900">
                    <button type="submit" class="btn-primary bg-white text-primary-600 hover:bg-gray-100 hover:text-primary-700">
                        <span class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            Follow Squad
                        </span>
                    </button>
                </div>

                <p class="text-sm text-primary-200 mt-4">Get player stats, match highlights, and exclusive content. Unsubscribe anytime.</p>
            </div>
        </div>
    </section> -->
@endsection

@push('scripts')
<script>
    const players = @json($playersByPosition->flatten());

    function openPlayerModal(playerId) {
        const player = players.find(p => p.id === playerId);
        if (!player) return;

        const modal = document.getElementById('playerModal');
        const content = document.getElementById('modalContent');

        // Enhanced modal content with modern design
        content.innerHTML = `
            <div class="flex flex-col lg:flex-row gap-8">
                <!-- Player Photo Section -->
                <div class="lg:w-1/2">
                    <div class="relative group">
                        ${player.photo ?
                            `<div class="image-zoom overflow-hidden rounded-2xl">
                                <img src="${player.photo_url}" alt="${player.name}" class="w-full h-96 object-cover transition-transform duration-700 group-hover:scale-110">
                            </div>` :
                            `<div class="w-full h-96 bg-gradient-to-br from-primary-500 via-primary-600 to-primary-700 flex items-center justify-center rounded-2xl relative overflow-hidden">
                                <div class="absolute inset-0 bg-gradient-to-br from-transparent to-black/20"></div>
                                <svg class="h-32 w-32 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>`
                        }

                        <!-- Jersey Number Overlay -->
                        <div class="absolute top-4 right-4 neomorphism w-16 h-16 flex items-center justify-center bg-white">
                            <span class="text-2xl font-black text-primary-600">#${player.shirt_number}</span>
                        </div>
                    </div>
                </div>

                <!-- Player Info Section -->
                <div class="lg:w-1/2">
                    <!-- Header -->
                    <div class="mb-6">
                        <h2 class="text-4xl font-black text-gray-900 mb-2">${player.name}</h2>
                        <div class="flex items-center space-x-4">
                            <span class="badge badge-primary text-lg px-4 py-2">${player.position_name}</span>
                            <span class="text-gray-600 flex items-center">
                                <span class="mr-2">🌍</span>
                                ${player.nationality}
                            </span>
                        </div>
                    </div>

                    <!-- Quick Info Grid -->
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="neomorphism p-4 text-center">
                            <div class="text-sm text-gray-600 mb-1">Jersey Number</div>
                            <div class="text-2xl font-bold text-primary-600">#${player.shirt_number}</div>
                        </div>
                        <div class="neomorphism p-4 text-center">
                            <div class="text-sm text-gray-600 mb-1">Age</div>
                            <div class="text-lg font-semibold text-gray-900">${player.age || 'N/A'}</div>
                        </div>
                    </div>

                    <!-- Social Media Links -->
                    ${player.social_media && (player.social_media.instagram || player.social_media.twitter || player.social_media.facebook) ? `
                        <div class="mb-6">
                            <h3 class="text-lg font-bold text-gray-900 mb-3 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                </svg>
                                Follow ${player.name.split(' ')[0]}
                            </h3>
                            <div class="flex space-x-3">
                                ${player.social_media.instagram ? `
                                    <a href="${player.social_media.instagram}" target="_blank" class="neomorphism p-3 hover:scale-110 transition-transform duration-300">
                                        <svg class="w-6 h-6 text-pink-600" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.097.118.112.221.085.341-.09.394-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
                                        </svg>
                                    </a>
                                ` : ''}
                                ${player.social_media.twitter ? `
                                    <a href="${player.social_media.twitter}" target="_blank" class="neomorphism p-3 hover:scale-110 transition-transform duration-300">
                                        <svg class="w-6 h-6 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                                        </svg>
                                    </a>
                                ` : ''}
                                ${player.social_media.facebook ? `
                                    <a href="${player.social_media.facebook}" target="_blank" class="neomorphism p-3 hover:scale-110 transition-transform duration-300">
                                        <svg class="w-6 h-6 text-blue-800" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                        </svg>
                                    </a>
                                ` : ''}
                            </div>
                        </div>
                    ` : ''}

                    <!-- Previous Clubs -->
                    ${player.previous_clubs && player.previous_clubs.length > 0 ? `
                        <div class="mb-6">
                            <h3 class="text-lg font-bold text-gray-900 mb-3 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                Previous Clubs
                            </h3>
                            <div class="space-y-2">
                                ${player.previous_clubs.map(club => `
                                    <div class="glass-card p-3 rounded-lg flex items-center">
                                        <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center mr-3">
                                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                            </svg>
                                        </div>
                                        <span class="text-gray-700 font-medium">${club.name}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}

                    ${player.bio ? `
                        <div class="mb-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-3 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                Biography
                            </h3>
                            <div class="glass-card p-4 rounded-xl">
                                <p class="text-gray-700 leading-relaxed">${player.bio}</p>
                            </div>
                        </div>
                    ` : ''}

                    ${player.stats ? `
                        <div>
                            <h3 class="text-xl font-bold text-gray-900 mb-4 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                Season Statistics
                            </h3>
                            <div class="grid grid-cols-3 gap-4">
                                ${player.stats.goals !== undefined ? `
                                    <div class="neomorphism p-4 text-center hover:scale-105 transition-transform duration-300">
                                        <div class="text-3xl font-bold text-primary-600 animate-score" data-score="${player.stats.goals}">${player.stats.goals}</div>
                                        <div class="text-sm text-gray-600 font-medium">Goals</div>
                                    </div>
                                ` : ''}
                                ${player.stats.assists !== undefined ? `
                                    <div class="neomorphism p-4 text-center hover:scale-105 transition-transform duration-300">
                                        <div class="text-3xl font-bold text-green-600 animate-score" data-score="${player.stats.assists}">${player.stats.assists}</div>
                                        <div class="text-sm text-gray-600 font-medium">Assists</div>
                                    </div>
                                ` : ''}
                                ${player.stats.appearances !== undefined ? `
                                    <div class="neomorphism p-4 text-center hover:scale-105 transition-transform duration-300">
                                        <div class="text-3xl font-bold text-blue-600 animate-score" data-score="${player.stats.appearances}">${player.stats.appearances}</div>
                                        <div class="text-sm text-gray-600 font-medium">Appearances</div>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    ` : ''}

                    <!-- Action Buttons -->
                    <div class="mt-8 flex space-x-4">
                        <button class="btn-primary flex-1 hover-glow">
                            <span class="flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                                Follow Player
                            </span>
                        </button>
                        <button class="btn-outline hover:scale-105 transition-transform duration-300">
                            <span class="flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                </svg>
                                Share
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        `;

        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';

        // Trigger animations for stats
        setTimeout(() => {
            const scoreElements = modal.querySelectorAll('.animate-score');
            scoreElements.forEach(el => {
                const score = parseInt(el.getAttribute('data-score'));
                animateScore(el, score);
            });
        }, 300);
    }

    function closePlayerModal(event) {
        if (event && event.target !== event.currentTarget) return;

        const modal = document.getElementById('playerModal');
        modal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }

    function animateScore(element, finalScore) {
        let currentScore = 0;
        const increment = finalScore / 20;

        function updateScore() {
            currentScore += increment;
            if (currentScore >= finalScore) {
                element.textContent = finalScore;
            } else {
                element.textContent = Math.floor(currentScore);
                requestAnimationFrame(updateScore);
            }
        }

        updateScore();
    }

    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closePlayerModal();
        }
    });
</script>
@endpush
