@extends('layouts.main')

@section('title', 'News')
@section('description', 'Stay updated with the latest news from Mbuni FC. Match reports, transfers, interviews and club updates.')

@section('content')
    <!-- Enhanced Page Header -->
    <section class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white py-20 overflow-hidden">
        <!-- Background Effects -->
        <div class="absolute inset-0">
            <!-- <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl"></div>
            <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div> -->
            <!-- Animated Football Icons -->
            <div class="absolute top-10 left-10 text-white/50 text-4xl animate-bounce">📰</div>
            <div class="absolute top-20 right-20 text-white/50 text-3xl animate-bounce" style="animation-delay: 1s;">🏆</div>
            <div class="absolute bottom-20 left-20 text-white/50 text-3xl animate-bounce" style="animation-delay: 2s;">ℹ️</div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center fade-in-up">
                <h1 class="text-5xl md:text-6xl font-black mb-6">
                    Latest <span class="text-gradient bg-gradient-to-r from-yellow-300 to-yellow-500 bg-clip-text text-transparent">News</span>
                </h1>
                <p class="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed">
                    Stay updated with everything happening at Mbuni FC - from match reports to exclusive interviews and behind-the-scenes content
                </p>

                <!-- Quick Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 max-w-2xl mx-auto">
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="{{ $siteSettings['total_articles'] ?? '150' }}">0</div>
                        <div class="text-sm text-white/70">Articles</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="{{ $siteSettings['total_reports'] ?? '25' }}">0</div>
                        <div class="text-sm text-white/70">Reports</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="{{ $siteSettings['total_categories'] ?? '12' }}">0</div>
                        <div class="text-sm text-white/70">Categories</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="{{ $siteSettings['total_readers'] ?? '100' }}">0</div>
                        <div class="text-sm text-white/70">Readers</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Filters and Search -->
    <section class="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 py-8 sticky top-20 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                <!-- Enhanced Category Filter -->
                <div class="flex flex-wrap gap-3 fade-in-left">
                    <a href="{{ route('news.index') }}"
                       class="px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 hover:scale-105 {{ !request('category') ? 'bg-gradient-to-r from-primary-600 to-primary-700 text-white shadow-lg' : 'neomorphism text-gray-700 hover:text-primary-600' }}">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                            </svg>
                            All News
                        </span>
                    </a>
                    @foreach($categories as $category)
                        <a href="{{ route('news.index', ['category' => $category->id]) }}"
                           class="px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 hover:scale-105 {{ request('category') == $category->id ? 'bg-gradient-to-r from-primary-600 to-primary-700 text-white shadow-lg' : 'neomorphism text-gray-700 hover:text-primary-600' }}"
                           style="border-left: 4px solid {{ $category->color }};">
                            <span class="flex items-center">
                                <div class="w-3 h-3 rounded-full mr-2" style="background-color: {{ $category->color }};"></div>
                                {{ $category->name }}
                            </span>
                        </a>
                    @endforeach
                </div>

                <!-- Enhanced Search Form -->
                <form method="GET" action="{{ route('news.index') }}" class="flex gap-3 fade-in-right">
                    @if(request('category'))
                        <input type="hidden" name="category" value="{{ request('category') }}">
                    @endif
                    <div class="relative">
                        <input type="text"
                               name="search"
                               value="{{ request('search') }}"
                               placeholder="Search news, players, matches..."
                               class="form-input-glow w-64 pl-12 pr-4 py-3 text-sm">
                        <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <button type="submit" class="btn-primary hover-glow">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Search
                        </span>
                    </button>
                    @if(request('search') || request('category'))
                        <a href="{{ route('news.index') }}" class="btn-secondary hover:scale-105 transition-transform duration-300">
                            <span class="flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Clear
                            </span>
                        </a>
                    @endif
                </form>
            </div>
        </div>
    </section>

    <!-- Enhanced News Grid -->
    <section class="py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
        <!-- Background Decorations -->
        <div class="absolute top-0 right-0 w-96 h-96 bg-primary-100 rounded-full blur-3xl opacity-30 translate-x-48 -translate-y-48"></div>
        <div class="absolute bottom-0 left-0 w-80 h-80 bg-yellow-100 rounded-full blur-3xl opacity-30 -translate-x-40 translate-y-40"></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            @if($news->count() > 0)
                <!-- Results Info -->
                <div class="text-center mb-12 fade-in-up">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">
                        @if(request('search'))
                            Search Results for "{{ request('search') }}"
                        @elseif(request('category'))
                            @php
                                $selectedCategory = $categories->firstWhere('id', request('category'));
                            @endphp
                            {{ $selectedCategory ? $selectedCategory->name : 'Category' }} News
                        @else
                            All News Articles
                        @endif
                    </h2>
                    <p class="text-gray-600">{{ $news->total() }} {{ Str::plural('article', $news->total()) }} found</p>
                </div>

                <!-- Masonry Grid Layout -->
                <div class="masonry-grid">
                    @foreach($news as $index => $article)
                        <article class="masonry-item card-modern hover-lift group fade-in-up" style="animation-delay: {{ $index * 0.1 }}s;">
                            <div class="relative overflow-hidden">
                                @if($article->image)
                                    <div class="image-zoom h-56 overflow-hidden">
                                        <img src="{{ $article->image_url }}" alt="{{ $article->title }}" class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">
                                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                    </div>
                                @else
                                    <div class="h-56 bg-gradient-to-br from-primary-500 via-primary-600 to-primary-700 flex items-center justify-center relative overflow-hidden">
                                        <div class="absolute inset-0 bg-gradient-to-br from-transparent to-black/20"></div>
                                        <div class="text-white text-6xl opacity-30">📰</div>
                                        <div class="absolute bottom-4 left-4 text-white">
                                            <div class="text-sm font-semibold">{{ $article->category_name }}</div>
                                        </div>
                                    </div>
                                @endif

                                <!-- Floating Elements -->
                                <div class="absolute top-4 left-4">
                                    <span class="badge badge-gray glass-card text-white font-semibold">
                                        {{ $article->newsCategory->name }}
                                    </span>
                                </div>

                                <div class="absolute top-4 right-4">
                                    <div class="glass-card px-3 py-1 text-white text-sm font-medium">
                                        {{ $article->published_at->format('M d') }}
                                    </div>
                                </div>

                                <!-- Reading Time -->
                                <div class="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    <div class="glass-card px-3 py-1 text-white text-xs">
                                        {{ ceil(str_word_count(strip_tags($article->content ?? $article->excerpt)) / 200) }} min read
                                    </div>
                                </div>
                            </div>

                            <div class="p-6">
                                <h2 class="text-xl font-bold mb-3 text-gray-900 group-hover:text-primary-600 transition-colors duration-300 line-clamp-2">
                                    <a href="{{ route('news.show', $article) }}" class="hover:underline">
                                        {{ $article->title }}
                                    </a>
                                </h2>

                                <p class="text-gray-600 mb-4 line-clamp-3">{{ $article->excerpt }}</p>

                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                                            {{ substr($article->author->name, 0, 1) }}
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">{{ $article->author->name }}</p>
                                            <div class="flex items-center space-x-2 text-xs text-gray-500">
                                                <span>{{ $article->published_at->diffForHumans(['short' => true]) }}</span>
                                                <span>•</span>
                                                <div class="flex items-center">
                                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                    </svg>
                                                    <span>{{ number_format($article->views) }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <a href="{{ route('news.show', $article) }}" class="inline-flex items-center text-primary-600 hover:text-primary-700 font-semibold text-sm group-hover:translate-x-1 transition-transform duration-300">
                                        Read More
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </a>
                                </div>

                                <!-- Tags -->
                                @if($article->tags ?? false)
                                    <div class="mt-4 pt-4 border-t border-gray-100">
                                        <div class="flex flex-wrap gap-2">
                                            @foreach(explode(',', $article->tags) as $tag)
                                                <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-lg hover:bg-primary-100 hover:text-primary-700 transition-colors duration-200">
                                                    #{{ trim($tag) }}
                                                </span>
                                            @endforeach
                                        </div>
                                    </div>
                                @endif
                            </div>

                            <!-- Hover Overlay -->
                            <div class="absolute inset-0 bg-gradient-to-t from-primary-600/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                        </article>
                    @endforeach
                </div>

                <!-- Enhanced Pagination -->
                <div class="mt-16 fade-in-up">
                    <div class="flex justify-center">
                        {{ $news->appends(request()->query())->links('pagination::tailwind') }}
                    </div>
                </div>

                <!-- Load More Button (Alternative to pagination) -->
                @if($news->hasMorePages())
                    <div class="text-center mt-12 fade-in-up">
                        <button class="btn-outline hover-glow" onclick="loadMoreNews()">
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                Load More Articles
                            </span>
                        </button>
                    </div>
                @endif
            @else
                <!-- Enhanced Empty State -->
                <div class="text-center py-20 fade-in-up">
                    <div class="max-w-md mx-auto">
                        <!-- Animated Icon -->
                        <div class="relative mb-8">
                            <div class="w-32 h-32 mx-auto bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                                <svg class="h-16 w-16 text-gray-400 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                </svg>
                            </div>
                            <div class="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm">!</span>
                            </div>
                        </div>

                        <h3 class="text-3xl font-bold text-gray-900 mb-4">
                            @if(request('search'))
                                No Results Found
                            @elseif(request('category'))
                                Category Empty
                            @else
                                No News Yet
                            @endif
                        </h3>

                        <p class="text-lg text-gray-600 mb-8">
                            @if(request('search'))
                                We couldn't find any articles matching "<strong>{{ request('search') }}</strong>". Try different keywords or browse all news.
                            @elseif(request('category'))
                                @php
                                    $selectedCategory = $categories->firstWhere('id', request('category'));
                                @endphp
                                No articles have been published in the <strong>{{ $selectedCategory ? $selectedCategory->name : 'selected' }}</strong> category yet.
                            @else
                                Our newsroom is preparing exciting content for you. Check back soon for the latest updates from Mbuni FC!
                            @endif
                        </p>

                        <div class="space-y-4">
                            @if(request('search') || request('category'))
                                <a href="{{ route('news.index') }}" class="btn-primary hover-glow inline-flex items-center">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                    </svg>
                                    View All News
                                </a>
                            @endif

                            <div class="flex justify-center space-x-4">
                                <a href="{{ route('fixtures.index') }}" class="btn-outline hover:scale-105 transition-transform duration-300">
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        Check Fixtures
                                    </span>
                                </a>
                                <a href="{{ route('squad.index') }}" class="btn-outline hover:scale-105 transition-transform duration-300">
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                        </svg>
                                        Meet the Squad
                                    </span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </section>

    <!-- Newsletter Signup Section -->
    <section class="py-16 bg-gradient-to-r from-primary-600 to-primary-700 text-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="fade-in-up">
                <h3 class="text-3xl font-bold mb-4">Never Miss a Story</h3>
                <p class="text-xl text-primary-100 mb-8">Get the latest Mbuni FC news delivered straight to your inbox</p>

                <form class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                    <input type="email" placeholder="Enter your email address" class="form-input-glow flex-1 text-gray-900">
                    <button type="submit" class="btn-primary bg-yellow-500 hover:bg-yellow-600">
                        <span class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            Subscribe
                        </span>
                    </button>
                </form>

                <p class="text-sm text-primary-200 mt-4">Join 500+ fans already subscribed. Unsubscribe anytime.</p>
            </div>
        </div>
    </section>
@endsection
