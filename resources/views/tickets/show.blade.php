@extends('layouts.main')

@section('title', 'Book Tickets - ' . $fixture->opponent . ' vs Mbuni FC')
@section('description', 'Book tickets for ' . $fixture->opponent . ' vs Mbuni FC on ' . $fixture->match_date->format('M d, Y'))

@section('content')
    <!-- Enhanced Match Header -->
    <section class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white py-20 overflow-hidden">
        <!-- Background Effects -->
        <div class="absolute inset-0">
            <!-- <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl"></div>
            <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div> -->
        </div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <!-- Breadcrumb -->
            <nav class="flex mb-8 fade-in-up" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="{{ route('home') }}" class="text-white/70 hover:text-white transition-colors duration-200">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-white/50" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <a href="{{ route('tickets.index') }}" class="ml-1 text-white/70 hover:text-white transition-colors duration-200 md:ml-2">Tickets</a>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-white/50" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="ml-1 text-white md:ml-2">Book Tickets</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Match Details -->
            <div class="text-center fade-in-up">
                <div class="inline-block glass-card px-4 py-2 mb-6">
                    <span class="text-yellow-300 font-semibold">{{ $fixture->competition }}</span>
                </div>
                
                <h1 class="text-4xl md:text-5xl font-black mb-6">
                    Book Your <span class="text-gradient bg-gradient-to-r from-yellow-300 to-yellow-500 bg-clip-text text-transparent">Tickets</span>
                </h1>
                
                <!-- Teams Display -->
                <div class="flex items-center justify-center max-w-2xl mx-auto mb-8">
                    <div class="text-center flex-1">
                        <div class="w-20 h-20 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-3">
                            <span class="text-3xl">{{ $fixture->home_or_away === 'home' ? '🏠' : '⚽' }}</span>
                        </div>
                        <div class="font-bold text-xl">{{ $fixture->home_or_away === 'home' ? 'Mbuni FC' : $fixture->opponent }}</div>
                    </div>
                    
                    <div class="text-center px-8">
                        <div class="text-5xl font-black text-yellow-300">VS</div>
                    </div>
                    
                    <div class="text-center flex-1">
                        <div class="w-20 h-20 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-3">
                            <span class="text-3xl">{{ $fixture->home_or_away === 'home' ? '🏃' : '🏠' }}</span>
                        </div>
                        <div class="font-bold text-xl">{{ $fixture->home_or_away === 'home' ? $fixture->opponent : 'Mbuni FC' }}</div>
                    </div>
                </div>
                
                <!-- Match Info -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-2xl mx-auto">
                    <div class="glass-card p-4 text-center">
                        <div class="text-lg font-bold text-yellow-300">{{ $fixture->match_date->format('M d, Y') }}</div>
                        <div class="text-sm text-white/70">Date</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-lg font-bold text-yellow-300">{{ $fixture->match_date->format('H:i') }}</div>
                        <div class="text-sm text-white/70">Kick-off</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-lg font-bold text-yellow-300">{{ Str::limit($fixture->venue, 15) }}</div>
                        <div class="text-sm text-white/70">Venue</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Booking Section -->
    <section class="py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
        <!-- Background Decorations -->
        <div class="absolute top-0 right-0 w-96 h-96 bg-primary-100 rounded-full blur-3xl opacity-30 translate-x-48 -translate-y-48"></div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            @if(session('success'))
                <!-- Success Message -->
                <div class="max-w-4xl mx-auto mb-12 fade-in-up">
                    <div class="glass-card p-8 bg-gradient-to-r from-green-500/10 to-green-600/10 border border-green-200">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <h3 class="text-2xl font-bold text-green-800">Booking Request Received!</h3>
                        </div>
                        <p class="text-green-700 mb-6">{{ session('success') }}</p>
                        
                        @if(session('booking_details'))
                            <div class="bg-white/50 rounded-lg p-6">
                                <h4 class="font-bold text-gray-900 mb-4">Booking Details:</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <div><strong>Match:</strong> {{ session('booking_details.fixture') }}</div>
                                    <div><strong>Date:</strong> {{ session('booking_details.date') }}</div>
                                    <div><strong>Venue:</strong> {{ session('booking_details.venue') }}</div>
                                    <div><strong>Ticket Type:</strong> {{ session('booking_details.ticket_type') }}</div>
                                    <div><strong>Quantity:</strong> {{ session('booking_details.quantity') }}</div>
                                    <div><strong>Total:</strong> TSh {{ number_format(session('booking_details.total_price')) }}</div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            @endif

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Ticket Selection -->
                <div class="fade-in-left">
                    <h2 class="text-3xl font-bold text-gray-900 mb-8">Select Your Tickets</h2>
                    
                    <div class="space-y-6">
                        @foreach($ticketTypes as $type => $details)
                            <div class="neomorphism p-6 {{ $details['available'] <= 0 ? 'opacity-50' : '' }}">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h3 class="text-xl font-bold text-gray-900">{{ $details['name'] }}</h3>
                                        <p class="text-gray-600">{{ $details['description'] }}</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-2xl font-bold text-primary-600">TSh {{ number_format($details['price']) }}</div>
                                        <div class="text-sm {{ $details['available'] > 0 ? 'text-green-600' : 'text-red-600' }}">
                                            {{ $details['available'] }} available
                                        </div>
                                    </div>
                                </div>
                                
                                @if($details['available'] > 0)
                                    <button class="btn-primary w-full" onclick="selectTicketType('{{ $type }}', '{{ $details['name'] }}', {{ $details['price'] }}, {{ $details['available'] }})">
                                        Select {{ $details['name'] }}
                                    </button>
                                @else
                                    <button class="btn-secondary w-full" disabled>
                                        Sold Out
                                    </button>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Booking Form -->
                <div class="fade-in-right">
                    <div class="glass-card p-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Booking Information</h2>
                        
                        <form method="POST" action="{{ route('tickets.book', $fixture) }}" id="booking-form">
                            @csrf
                            
                            <!-- Selected Ticket Display -->
                            <div id="selected-ticket" class="hidden mb-6 p-4 bg-primary-50 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="font-semibold text-primary-900" id="selected-type"></div>
                                        <div class="text-sm text-primary-700" id="selected-price"></div>
                                    </div>
                                    <button type="button" onclick="clearSelection()" class="text-primary-600 hover:text-primary-800">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            
                            <input type="hidden" name="ticket_type" id="ticket_type" required>
                            
                            <!-- Quantity -->
                            <div class="mb-6">
                                <label for="quantity" class="block text-sm font-medium text-gray-700 mb-2">Number of Tickets</label>
                                <select name="quantity" id="quantity" class="form-input-glow w-full" required onchange="updateTotal()">
                                    <option value="">Select quantity</option>
                                    <option value="1">1 ticket</option>
                                    <option value="2">2 tickets</option>
                                    <option value="3">3 tickets</option>
                                    <option value="4">4 tickets</option>
                                </select>
                                @error('quantity')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <!-- Total Price Display -->
                            <div id="total-display" class="hidden mb-6 p-4 bg-green-50 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <span class="font-semibold text-green-900">Total Price:</span>
                                    <span class="text-2xl font-bold text-green-600" id="total-price">TSh 0</span>
                                </div>
                            </div>
                            
                            <!-- Contact Information -->
                            <div class="space-y-4 mb-6">
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                                    <input type="text" name="name" id="name" value="{{ old('name') }}" class="form-input-glow w-full" required>
                                    @error('name')
                                        <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                                
                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                                    <input type="email" name="email" id="email" value="{{ old('email') }}" class="form-input-glow w-full" required>
                                    @error('email')
                                        <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                                
                                <div>
                                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                                    <input type="tel" name="phone" id="phone" value="{{ old('phone') }}" class="form-input-glow w-full" required>
                                    @error('phone')
                                        <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                            
                            <!-- Submit Button -->
                            <button type="submit" class="btn-primary w-full hover-glow" id="submit-btn" disabled>
                                <span class="flex items-center justify-center">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5h2.5a2.5 2.5 0 0 1 0 5H5m0 0h2.5a2.5 2.5 0 0 1 0 5H5"></path>
                                    </svg>
                                    Request Booking
                                </span>
                            </button>
                            
                            <p class="text-sm text-gray-600 mt-4 text-center">
                                We'll contact you within 24 hours to confirm your booking and arrange payment.
                            </p>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('scripts')
<script>
    let selectedTicketPrice = 0;
    let maxQuantity = 0;
    
    function selectTicketType(type, name, price, available) {
        // Update form
        document.getElementById('ticket_type').value = type;
        
        // Show selected ticket
        document.getElementById('selected-ticket').classList.remove('hidden');
        document.getElementById('selected-type').textContent = name;
        document.getElementById('selected-price').textContent = 'TSh ' + price.toLocaleString();
        
        // Update quantity options
        const quantitySelect = document.getElementById('quantity');
        quantitySelect.innerHTML = '<option value="">Select quantity</option>';
        
        const maxQty = Math.min(4, available);
        for (let i = 1; i <= maxQty; i++) {
            quantitySelect.innerHTML += `<option value="${i}">${i} ticket${i > 1 ? 's' : ''}</option>`;
        }
        
        selectedTicketPrice = price;
        maxQuantity = available;
        
        updateSubmitButton();
    }
    
    function clearSelection() {
        document.getElementById('selected-ticket').classList.add('hidden');
        document.getElementById('ticket_type').value = '';
        document.getElementById('quantity').value = '';
        document.getElementById('total-display').classList.add('hidden');
        selectedTicketPrice = 0;
        updateSubmitButton();
    }
    
    function updateTotal() {
        const quantity = parseInt(document.getElementById('quantity').value) || 0;
        
        if (quantity > 0 && selectedTicketPrice > 0) {
            const total = quantity * selectedTicketPrice;
            document.getElementById('total-price').textContent = 'TSh ' + total.toLocaleString();
            document.getElementById('total-display').classList.remove('hidden');
        } else {
            document.getElementById('total-display').classList.add('hidden');
        }
        
        updateSubmitButton();
    }
    
    function updateSubmitButton() {
        const ticketType = document.getElementById('ticket_type').value;
        const quantity = document.getElementById('quantity').value;
        const name = document.getElementById('name').value;
        const email = document.getElementById('email').value;
        const phone = document.getElementById('phone').value;
        
        const submitBtn = document.getElementById('submit-btn');
        
        if (ticketType && quantity && name && email && phone) {
            submitBtn.disabled = false;
            submitBtn.classList.remove('opacity-50');
        } else {
            submitBtn.disabled = true;
            submitBtn.classList.add('opacity-50');
        }
    }
    
    // Add event listeners for form validation
    document.addEventListener('DOMContentLoaded', function() {
        const inputs = ['name', 'email', 'phone'];
        inputs.forEach(id => {
            document.getElementById(id).addEventListener('input', updateSubmitButton);
        });
    });
</script>
@endpush
