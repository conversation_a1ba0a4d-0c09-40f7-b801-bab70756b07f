@extends('layouts.main')

@section('title', 'Match Tickets')
@section('description', 'Purchase tickets for Mbuni FC matches. Secure your seat and support the team.')

@section('content')
    <!-- Enhanced Tickets Header -->
    <section class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white py-20 -mt-20 overflow-hidden">
        <!-- Background Effects -->
        <div class="absolute inset-0">
            <!-- <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl"></div>
            <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div> -->
            <!-- Animated Ticket Icons -->
            <div class="absolute top-10 left-10 text-white/50 text-4xl animate-bounce">🎫</div>
            <div class="absolute top-20 right-20 text-white/50 text-3xl animate-bounce" style="animation-delay: 1s;">🏟️</div>
            <div class="absolute bottom-20 left-20 text-white/50 text-3xl animate-bounce" style="animation-delay: 2s;">⚽</div>
        </div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center fade-in-up">
                <h1 class="text-5xl md:text-6xl font-black mb-6">
                    Match <span class="text-gradient bg-gradient-to-r from-yellow-300 to-yellow-500 bg-clip-text text-transparent">Tickets</span>
                </h1>
                <p class="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed mb-8">
                    Secure your seat and be part of the electric atmosphere at Mbuni FC matches
                </p>
                
                <!-- Ticket Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto">
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="{{ $upcomingFixtures->count() }}">0</div>
                        <div class="text-sm text-white/70">Upcoming</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="550">0</div>
                        <div class="text-sm text-white/70">Capacity</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="15">0</div>
                        <div class="text-sm text-white/70">From TSh</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="100">0</div>
                        <div class="text-sm text-white/70">% Support</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Ticket Types Section -->


    <section class="py-16 bg-gray-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="bg-white rounded-lg shadow-lg p-8 md:p-12">
                <!-- Maintenance Icon -->
                <div class="mx-auto w-24 h-24 bg-yellow-100 rounded-full flex items-center justify-center mb-6">
                    <svg class="w-12 h-12 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-gray-900 mb-4">Ticketing System Under Maintenance</h2>
                
                <p class="text-lg text-gray-600 mb-8">
                    Our online ticketing system is currently undergoing maintenance to serve you better. 
                    We're working with our ticketing partner to provide you with the best possible experience.
                </p>

                <!-- Alternative Options -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <div class="bg-gray-50 rounded-lg p-6">
                        <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Call Us</h3>
                        <p class="text-gray-600 mb-3">Purchase tickets over the phone</p>
                        <p class="text-primary-600 font-semibold">+255 123 456 789</p>
                        <p class="text-sm text-gray-500">Mon-Fri: 9AM-5PM</p>
                    </div>

                    <div class="bg-gray-50 rounded-lg p-6">
                        <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Visit Stadium</h3>
                        <p class="text-gray-600 mb-3">Buy tickets at the stadium box office</p>
                        <p class="text-primary-600 font-semibold">Sheikh Amri Abeid Memorial Stadium</p>
                        <p class="text-sm text-gray-500">Match days: 2 hours before kickoff</p>
                    </div>
                </div>

                <!-- Contact Information -->
                <!-- <div class="bg-primary-50 rounded-lg p-6 mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Need Help?</h3>
                    <p class="text-gray-600 mb-4">
                        For any ticketing inquiries or assistance, please don't hesitate to contact us:
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="mailto:<EMAIL>" class="inline-flex items-center justify-center px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            Email Us
                        </a>
                        <a href="{{ route('contact.index') }}" class="inline-flex items-center justify-center px-6 py-3 bg-white text-primary-600 border-2 border-primary-600 rounded-lg hover:bg-primary-50 transition-colors">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                            Contact Form
                        </a>
                    </div>
                </div> -->

                <!-- Expected Return -->
                <div class="text-center">
                    <p class="text-gray-600 mb-4">
                        We expect our online ticketing system to be back online soon. 
                        Thank you for your patience and continued support!
                    </p>
                    <a href="{{ route('fixtures.index', ['status' => 'upcoming']) }}" class="btn-outline">
                        View Upcoming Matches
                    </a>
                </div>
            </div>
        </div>
    </section>    

    <!-- Contact Section -->
    <!-- <section class="py-16 bg-gradient-to-r from-primary-600 to-primary-700 text-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="fade-in-up">
                <h3 class="text-3xl font-bold mb-4">Need Help with Tickets?</h3>
                <p class="text-xl text-primary-100 mb-8">Our ticket support team is here to help you secure the best seats</p>
                
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="tel:+255123456789" class="btn-primary bg-white text-primary-600 hover:bg-gray-100 hover:text-primary-700">
                        <span class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                            Call +255 123 456 789
                        </span>
                    </a>
                    <a href="{{ route('contact.index') }}" class="btn-outline border-white text-white hover:bg-white hover:text-primary-600">
                        <span class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            Contact Form
                        </span>
                    </a>
                </div>
                
                <p class="text-sm text-primary-200 mt-4">Available Mon-Fri: 9AM-5PM, Match days: 2 hours before kickoff</p>
            </div>
        </div>
    </section> -->
@endsection

@push('scripts')
<script>
    function scrollToMatches() {
        const matchesSection = document.getElementById('matches-section');
        if (matchesSection) {
            matchesSection.scrollIntoView({ behavior: 'smooth' });
        } else {
            // If no matches section, scroll to contact
            document.querySelector('section:last-of-type').scrollIntoView({ behavior: 'smooth' });
        }
    }
</script>
@endpush
