/**
 * Responsive Design Enhancements
 * Mbuni FC Website - Cross-device Optimization
 */

class ResponsiveManager {
    constructor() {
        this.breakpoints = {
            mobile: 640,
            tablet: 768,
            desktop: 1024,
            large: 1280
        };
        this.currentBreakpoint = this.getCurrentBreakpoint();
        this.init();
    }

    init() {
        this.setupViewportHandler();
        this.setupTouchHandlers();
        this.setupOrientationHandler();
        this.optimizeForDevice();
    }

    getCurrentBreakpoint() {
        const width = window.innerWidth;
        if (width < this.breakpoints.mobile) return 'mobile';
        if (width < this.breakpoints.tablet) return 'tablet';
        if (width < this.breakpoints.desktop) return 'desktop';
        return 'large';
    }

    setupViewportHandler() {
        let resizeTimer;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                const newBreakpoint = this.getCurrentBreakpoint();
                if (newBreakpoint !== this.currentBreakpoint) {
                    this.currentBreakpoint = newBreakpoint;
                    this.handleBreakpointChange();
                }
            }, 250);
        });
    }

    setupTouchHandlers() {
        if ('ontouchstart' in window) {
            document.body.classList.add('touch-device');
            this.setupSwipeGestures();
        }
    }

    setupSwipeGestures() {
        let startX, startY, endX, endY;

        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        }, { passive: true });

        document.addEventListener('touchend', (e) => {
            endX = e.changedTouches[0].clientX;
            endY = e.changedTouches[0].clientY;
            this.handleSwipe(startX, startY, endX, endY);
        }, { passive: true });
    }

    handleSwipe(startX, startY, endX, endY) {
        const deltaX = endX - startX;
        const deltaY = endY - startY;
        const minSwipeDistance = 50;

        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {
            if (deltaX > 0) {
                this.triggerSwipeRight();
            } else {
                this.triggerSwipeLeft();
            }
        }
    }

    triggerSwipeLeft() {
        // Handle swipe left (e.g., next carousel item)
        const carousels = document.querySelectorAll('.carousel-container');
        carousels.forEach(carousel => {
            const nextBtn = carousel.querySelector('.carousel-next');
            if (nextBtn) nextBtn.click();
        });
    }

    triggerSwipeRight() {
        // Handle swipe right (e.g., previous carousel item)
        const carousels = document.querySelectorAll('.carousel-container');
        carousels.forEach(carousel => {
            const prevBtn = carousel.querySelector('.carousel-prev');
            if (prevBtn) prevBtn.click();
        });
    }

    setupOrientationHandler() {
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.handleOrientationChange();
            }, 100);
        });
    }

    handleOrientationChange() {
        // Recalculate layouts after orientation change
        const masonry = document.querySelectorAll('.masonry-grid');
        masonry.forEach(grid => {
            // Trigger reflow for masonry layouts
            grid.style.columnCount = '';
            setTimeout(() => {
                grid.style.columnCount = this.getMasonryColumns();
            }, 100);
        });
    }

    getMasonryColumns() {
        switch (this.currentBreakpoint) {
            case 'mobile': return '1';
            case 'tablet': return '2';
            case 'desktop': return '3';
            default: return '4';
        }
    }

    handleBreakpointChange() {
        this.optimizeForDevice();
        this.updateCarousels();
        this.updateNavigation();
    }

    optimizeForDevice() {
        const body = document.body;
        
        // Remove all breakpoint classes
        Object.keys(this.breakpoints).forEach(bp => {
            body.classList.remove(`bp-${bp}`);
        });
        
        // Add current breakpoint class
        body.classList.add(`bp-${this.currentBreakpoint}`);

        // Device-specific optimizations
        if (this.currentBreakpoint === 'mobile') {
            this.optimizeForMobile();
        } else if (this.currentBreakpoint === 'tablet') {
            this.optimizeForTablet();
        } else {
            this.optimizeForDesktop();
        }
    }

    optimizeForMobile() {
        // Reduce animation complexity
        const complexAnimations = document.querySelectorAll('.tilt-3d, .parallax');
        complexAnimations.forEach(el => {
            el.style.transform = 'none';
            el.style.transition = 'none';
        });

        // Simplify glassmorphism
        const glassElements = document.querySelectorAll('.glass-card');
        glassElements.forEach(el => {
            el.style.backdropFilter = 'blur(5px)';
        });

        // Hide decorative particles
        const particles = document.querySelectorAll('.particle');
        particles.forEach(p => p.style.display = 'none');
    }

    optimizeForTablet() {
        // Moderate optimizations for tablets
        const glassElements = document.querySelectorAll('.glass-card');
        glassElements.forEach(el => {
            el.style.backdropFilter = 'blur(10px)';
        });
    }

    optimizeForDesktop() {
        // Full effects for desktop
        const complexAnimations = document.querySelectorAll('.tilt-3d, .parallax');
        complexAnimations.forEach(el => {
            el.style.transform = '';
            el.style.transition = '';
        });

        const particles = document.querySelectorAll('.particle');
        particles.forEach(p => p.style.display = '');
    }

    updateCarousels() {
        const carousels = document.querySelectorAll('.carousel-container');
        carousels.forEach(carousel => {
            const itemsPerView = this.getCarouselItemsPerView();
            carousel.dataset.itemsPerView = itemsPerView;
        });
    }

    getCarouselItemsPerView() {
        switch (this.currentBreakpoint) {
            case 'mobile': return '1';
            case 'tablet': return '2';
            case 'desktop': return '3';
            default: return '4';
        }
    }

    updateNavigation() {
        const navbar = document.querySelector('.navbar');
        const mobileMenu = document.querySelector('.mobile-menu');
        
        if (this.currentBreakpoint === 'mobile' || this.currentBreakpoint === 'tablet') {
            if (navbar) navbar.classList.add('mobile-nav');
            if (mobileMenu) mobileMenu.classList.remove('hidden');
        } else {
            if (navbar) navbar.classList.remove('mobile-nav');
            if (mobileMenu) mobileMenu.classList.add('hidden');
        }
    }
}

// Accessibility enhancements
class AccessibilityManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupKeyboardNavigation();
        this.setupFocusManagement();
        this.setupScreenReaderSupport();
    }

    setupKeyboardNavigation() {
        // Enhanced keyboard navigation for carousels
        document.addEventListener('keydown', (e) => {
            if (e.target.classList.contains('carousel-container')) {
                if (e.key === 'ArrowLeft') {
                    const prevBtn = e.target.querySelector('.carousel-prev');
                    if (prevBtn) prevBtn.click();
                } else if (e.key === 'ArrowRight') {
                    const nextBtn = e.target.querySelector('.carousel-next');
                    if (nextBtn) nextBtn.click();
                }
            }
        });
    }

    setupFocusManagement() {
        // Trap focus in modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                const modal = document.querySelector('.modal-backdrop:not(.hidden)');
                if (modal) {
                    this.trapFocus(e, modal);
                }
            }
        });
    }

    trapFocus(e, container) {
        const focusableElements = container.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
        }
    }

    setupScreenReaderSupport() {
        // Add ARIA labels to interactive elements
        const interactiveElements = document.querySelectorAll('.cursor-ripple, .hover-lift');
        interactiveElements.forEach(el => {
            if (!el.getAttribute('aria-label') && !el.getAttribute('aria-labelledby')) {
                el.setAttribute('aria-label', 'Interactive element');
            }
        });
    }
}

// Initialize responsive and accessibility managers
document.addEventListener('DOMContentLoaded', () => {
    new ResponsiveManager();
    new AccessibilityManager();
});

// Export for global access
window.MbuniFC = window.MbuniFC || {};
window.MbuniFC.Responsive = { ResponsiveManager, AccessibilityManager };
