/**
 * Performance Optimizations & Final Polish
 * Mbuni FC Website - Enhanced User Experience
 */

// Intersection Observer for lazy loading animations
class AnimationObserver {
    constructor() {
        this.observer = null;
        this.init();
    }

    init() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver(
                (entries) => this.handleIntersection(entries),
                {
                    threshold: 0.1,
                    rootMargin: '50px 0px -50px 0px'
                }
            );
            this.observeElements();
        } else {
            // Fallback for older browsers
            this.fallbackAnimation();
        }
    }

    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
                this.observer.unobserve(entry.target);
            }
        });
    }

    observeElements() {
        const elements = document.querySelectorAll(
            '.fade-in-up, .fade-in-left, .fade-in-right, .scale-in'
        );
        elements.forEach(el => this.observer.observe(el));
    }

    fallbackAnimation() {
        const elements = document.querySelectorAll(
            '.fade-in-up, .fade-in-left, .fade-in-right, .scale-in'
        );
        elements.forEach(el => el.classList.add('animate'));
    }
}

// Performance-optimized image lazy loading
class LazyImageLoader {
    constructor() {
        this.images = document.querySelectorAll('img[data-src]');
        this.imageObserver = null;
        this.init();
    }

    init() {
        if ('IntersectionObserver' in window) {
            this.imageObserver = new IntersectionObserver(
                (entries) => this.loadImages(entries),
                { rootMargin: '50px 0px' }
            );
            this.images.forEach(img => this.imageObserver.observe(img));
        } else {
            this.loadAllImages();
        }
    }

    loadImages(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                this.loadImage(img);
                this.imageObserver.unobserve(img);
            }
        });
    }

    loadImage(img) {
        const src = img.getAttribute('data-src');
        if (src) {
            img.src = src;
            img.removeAttribute('data-src');
            img.classList.add('loaded');
        }
    }

    loadAllImages() {
        this.images.forEach(img => this.loadImage(img));
    }
}

// Optimized scroll handler with throttling
class ScrollHandler {
    constructor() {
        this.isScrolling = false;
        this.init();
    }

    init() {
        window.addEventListener('scroll', () => this.handleScroll(), { passive: true });
    }

    handleScroll() {
        if (!this.isScrolling) {
            requestAnimationFrame(() => {
                this.updateScrollEffects();
                this.isScrolling = false;
            });
            this.isScrolling = true;
        }
    }

    updateScrollEffects() {
        const scrollY = window.pageYOffset;
        
        // Parallax effects
        const parallaxElements = document.querySelectorAll('.parallax');
        parallaxElements.forEach(el => {
            const speed = el.dataset.speed || 0.5;
            const yPos = -(scrollY * speed);
            el.style.transform = `translateY(${yPos}px)`;
        });

        // Navbar background opacity
        const navbar = document.querySelector('.navbar-scroll');
        if (navbar) {
            const opacity = Math.min(scrollY / 100, 1);
            navbar.style.backgroundColor = `rgba(255, 255, 255, ${opacity})`;
        }
    }
}

// Loading skeleton manager
class SkeletonLoader {
    static show(container, type = 'default') {
        const skeletons = {
            default: this.createDefaultSkeleton(),
            card: this.createCardSkeleton(),
            list: this.createListSkeleton(),
            profile: this.createProfileSkeleton()
        };

        container.innerHTML = skeletons[type] || skeletons.default;
    }

    static hide(container, content) {
        setTimeout(() => {
            container.innerHTML = content;
        }, 300);
    }

    static createDefaultSkeleton() {
        return `
            <div class="skeleton skeleton-text-lg"></div>
            <div class="skeleton skeleton-text"></div>
            <div class="skeleton skeleton-text"></div>
            <div class="skeleton skeleton-button"></div>
        `;
    }

    static createCardSkeleton() {
        return `
            <div class="skeleton skeleton-card"></div>
            <div class="skeleton skeleton-text-lg"></div>
            <div class="skeleton skeleton-text"></div>
        `;
    }

    static createListSkeleton() {
        return Array(5).fill().map(() => `
            <div class="flex items-center space-x-3 mb-4">
                <div class="skeleton skeleton-avatar"></div>
                <div class="flex-1">
                    <div class="skeleton skeleton-text"></div>
                    <div class="skeleton skeleton-text w-3/4"></div>
                </div>
            </div>
        `).join('');
    }

    static createProfileSkeleton() {
        return `
            <div class="flex items-center space-x-4 mb-6">
                <div class="skeleton skeleton-avatar w-16 h-16"></div>
                <div class="flex-1">
                    <div class="skeleton skeleton-text-lg"></div>
                    <div class="skeleton skeleton-text w-1/2"></div>
                </div>
            </div>
            <div class="skeleton skeleton-card"></div>
        `;
    }
}

// Performance monitoring
class PerformanceMonitor {
    constructor() {
        this.metrics = {};
        this.init();
    }

    init() {
        if ('performance' in window) {
            this.measurePageLoad();
            this.measureInteractions();
        }
    }

    measurePageLoad() {
        window.addEventListener('load', () => {
            const navigation = performance.getEntriesByType('navigation')[0];
            this.metrics.loadTime = navigation.loadEventEnd - navigation.loadEventStart;
            this.metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
            
            // Log performance metrics (in production, send to analytics)
            console.log('Performance Metrics:', this.metrics);
        });
    }

    measureInteractions() {
        // Measure click response times
        document.addEventListener('click', (e) => {
            const startTime = performance.now();
            requestAnimationFrame(() => {
                const endTime = performance.now();
                const responseTime = endTime - startTime;
                
                if (responseTime > 16) { // More than one frame
                    console.warn(`Slow interaction detected: ${responseTime}ms`);
                }
            });
        });
    }
}

// Optimized ripple effect
class RippleEffect {
    static create(element, event) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple-effect');

        element.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    static init() {
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('cursor-ripple')) {
                this.create(e.target, e);
            }
        });
    }
}

// Initialize all performance optimizations
document.addEventListener('DOMContentLoaded', () => {
    // Initialize observers and handlers
    new AnimationObserver();
    new LazyImageLoader();
    new ScrollHandler();
    new PerformanceMonitor();
    
    // Initialize ripple effects
    RippleEffect.init();
    
    // Preload critical resources
    const criticalImages = document.querySelectorAll('img[data-critical]');
    criticalImages.forEach(img => {
        const src = img.getAttribute('data-src');
        if (src) {
            const preloadImg = new Image();
            preloadImg.src = src;
        }
    });
    
    // Remove loading overlay
    const loadingOverlay = document.querySelector('.loading-overlay');
    if (loadingOverlay) {
        setTimeout(() => {
            loadingOverlay.classList.add('hidden');
        }, 500);
    }
});

// Export for use in other modules
window.MbuniFC = window.MbuniFC || {};
window.MbuniFC.Performance = {
    AnimationObserver,
    LazyImageLoader,
    ScrollHandler,
    SkeletonLoader,
    PerformanceMonitor,
    RippleEffect
};
