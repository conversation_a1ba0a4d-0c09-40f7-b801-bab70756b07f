/**
 * Final Polish & Micro-interactions
 * Mbuni FC Website - Enhanced User Experience
 */

// Enhanced micro-interactions
class MicroInteractions {
    constructor() {
        this.init();
    }

    init() {
        this.setupButtonInteractions();
        this.setupCardInteractions();
        this.setupFormInteractions();
        this.setupNavigationInteractions();
    }

    setupButtonInteractions() {
        // Enhanced button hover effects
        const buttons = document.querySelectorAll('.btn-primary, .btn-secondary, .btn-outline');
        buttons.forEach(btn => {
            btn.addEventListener('mouseenter', (e) => {
                this.createButtonGlow(e.target);
            });

            btn.addEventListener('mouseleave', (e) => {
                this.removeButtonGlow(e.target);
            });

            btn.addEventListener('click', (e) => {
                this.createButtonRipple(e.target, e);
            });
        });
    }

    createButtonGlow(button) {
        button.style.boxShadow = '0 0 20px rgba(220, 38, 38, 0.4)';
        button.style.transform = 'translateY(-2px)';
    }

    removeButtonGlow(button) {
        button.style.boxShadow = '';
        button.style.transform = '';
    }

    createButtonRipple(button, event) {
        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('button-ripple');

        button.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    setupCardInteractions() {
        const cards = document.querySelectorAll('.card-modern, .glass-card, .neomorphism');
        cards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-8px) scale(1.02)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = '';
            });
        });
    }

    setupFormInteractions() {
        const inputs = document.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('focus', (e) => {
                this.enhanceFocusEffect(e.target);
            });

            input.addEventListener('blur', (e) => {
                this.removeFocusEffect(e.target);
            });
        });
    }

    enhanceFocusEffect(input) {
        const parent = input.parentElement;
        parent.style.transform = 'scale(1.02)';
        input.style.boxShadow = '0 0 0 3px rgba(220, 38, 38, 0.1)';
    }

    removeFocusEffect(input) {
        const parent = input.parentElement;
        parent.style.transform = '';
        input.style.boxShadow = '';
    }

    setupNavigationInteractions() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('mouseenter', (e) => {
                this.createNavUnderline(e.target);
            });

            link.addEventListener('mouseleave', (e) => {
                this.removeNavUnderline(e.target);
            });
        });
    }

    createNavUnderline(link) {
        if (!link.querySelector('.nav-underline')) {
            const underline = document.createElement('div');
            underline.className = 'nav-underline';
            underline.style.cssText = `
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 2px;
                background: linear-gradient(90deg, #dc2626, #fbbf24);
                transform: scaleX(0);
                transition: transform 0.3s ease;
            `;
            link.style.position = 'relative';
            link.appendChild(underline);
        }
        
        const underline = link.querySelector('.nav-underline');
        underline.style.transform = 'scaleX(1)';
    }

    removeNavUnderline(link) {
        const underline = link.querySelector('.nav-underline');
        if (underline) {
            underline.style.transform = 'scaleX(0)';
        }
    }
}

// Enhanced scroll animations
class ScrollAnimations {
    constructor() {
        this.elements = [];
        this.init();
    }

    init() {
        this.setupScrollTriggers();
        this.setupParallaxEffects();
        this.setupCounterAnimations();
    }

    setupScrollTriggers() {
        const observer = new IntersectionObserver(
            (entries) => this.handleScrollTrigger(entries),
            { threshold: 0.1, rootMargin: '0px 0px -50px 0px' }
        );

        const elements = document.querySelectorAll('[data-scroll-trigger]');
        elements.forEach(el => observer.observe(el));
    }

    handleScrollTrigger(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const animation = element.dataset.scrollTrigger;
                element.classList.add(`animate-${animation}`);
            }
        });
    }

    setupParallaxEffects() {
        const parallaxElements = document.querySelectorAll('[data-parallax]');
        
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            
            parallaxElements.forEach(el => {
                const speed = parseFloat(el.dataset.parallax) || 0.5;
                const yPos = -(scrolled * speed);
                el.style.transform = `translateY(${yPos}px)`;
            });
        }, { passive: true });
    }

    setupCounterAnimations() {
        const counters = document.querySelectorAll('[data-counter]');
        const observer = new IntersectionObserver(
            (entries) => this.animateCounters(entries),
            { threshold: 0.5 }
        );

        counters.forEach(counter => observer.observe(counter));
    }

    animateCounters(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting && !entry.target.classList.contains('counted')) {
                this.animateCounter(entry.target);
                entry.target.classList.add('counted');
            }
        });
    }

    animateCounter(element) {
        const target = parseInt(element.dataset.counter);
        const duration = 2000;
        const increment = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, 16);
    }
}

// Enhanced loading states
class LoadingStates {
    constructor() {
        this.init();
    }

    init() {
        this.setupFormLoading();
        this.setupImageLoading();
        this.setupContentLoading();
    }

    setupFormLoading() {
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                this.showFormLoading(form);
            });
        });
    }

    showFormLoading(form) {
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = `
                <svg class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
            `;
            submitBtn.disabled = true;
        }
    }

    setupImageLoading() {
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(img => {
            img.addEventListener('load', () => {
                img.classList.add('loaded');
            });
        });
    }

    setupContentLoading() {
        // Show skeleton loaders for dynamic content
        const dynamicContainers = document.querySelectorAll('[data-dynamic-content]');
        dynamicContainers.forEach(container => {
            this.showSkeleton(container);
        });
    }

    showSkeleton(container) {
        const skeletonHTML = `
            <div class="animate-pulse">
                <div class="skeleton skeleton-text-lg mb-4"></div>
                <div class="skeleton skeleton-text mb-2"></div>
                <div class="skeleton skeleton-text mb-2"></div>
                <div class="skeleton skeleton-text w-3/4"></div>
            </div>
        `;
        container.innerHTML = skeletonHTML;
    }
}

// Initialize all enhancements
document.addEventListener('DOMContentLoaded', () => {
    new MicroInteractions();
    new ScrollAnimations();
    new LoadingStates();

    // Add CSS for button ripple effect
    const style = document.createElement('style');
    style.textContent = `
        .button-ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        }
        
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
});

// Export for global access
window.MbuniFC = window.MbuniFC || {};
window.MbuniFC.FinalPolish = {
    MicroInteractions,
    ScrollAnimations,
    LoadingStates
};
